/**
 * LINE Flex Message 模板
 * 用於發送訂單確認訊息
 */

import { sendMessages } from './liff';

interface OrderItem {
  id: number;
  productId: number;
  quantity: number;
  price: number;
  customizations: any;
  product: {
    id: number;
    name: string;
    price: number;
    image?: string;
  };
}

interface Order {
  id: number;
  orderNumber: string;
  totalPrice: number;
  status: string;
  orderType: 'immediate' | 'scheduled';
  scheduledTime?: string;
  note?: string;
  items: OrderItem[];
  createdAt: string;
}

/**
 * 創建訂單確認 Flex Message
 */
export function createOrderConfirmationFlexMessage(order: Order) {
  // 格式化時間
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-TW', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 格式化訂單類型
  const getOrderTypeText = (orderType: string, scheduledTime?: string) => {
    if (orderType === 'scheduled' && scheduledTime) {
      return `預約訂購 - ${formatDateTime(scheduledTime)}`;
    }
    return '立即訂購';
  };

  // 創建商品列表
  const createItemsContent = (items: OrderItem[]) => {
    if (!items || !Array.isArray(items)) {
      return [];
    }
    return items.map((item, index) => {
      // 格式化客製化選項
      const customizationsText = item.customizations
        ? Object.entries(item.customizations)
            .filter(([key, value]) => value !== null && value !== undefined)
            .map(([key, value]) => {
              if (key === 'addOns' && Array.isArray(value)) {
                return value.map((addon: any) => `+${addon.name}`).join(', ');
              }
              return `${value}`;
            })
            .join(' | ')
        : '';

      return {
        type: "box",
        layout: "vertical",
        contents: [
          {
            type: "box",
            layout: "horizontal",
            contents: [
              {
                type: "text",
                text: `${item.product.name}`,
                weight: "bold",
                size: "sm",
                flex: 3
              },
              {
                type: "text",
                text: `x${item.quantity}`,
                size: "sm",
                color: "#666666",
                flex: 1,
                align: "end"
              },
              {
                type: "text",
                text: `NT$${item.price}`,
                size: "sm",
                color: "#666666",
                flex: 1,
                align: "end"
              }
            ]
          },
          ...(customizationsText ? [{
            type: "text",
            text: customizationsText,
            size: "xs",
            color: "#999999",
            wrap: true,
            margin: "xs"
          }] : []),
          ...(index < items.length - 1 ? [{
            type: "separator",
            margin: "md"
          }] : [])
        ],
        margin: index > 0 ? "md" : "none"
      };
    });
  };

  const flexMessage = {
    type: "flex",
    altText: `訂單確認 - ${order.orderNumber}`,
    contents: {
      type: "bubble",
      header: {
        type: "box",
        layout: "vertical",
        contents: [
          {
            type: "text",
            text: "🎉 訂單確認",
            weight: "bold",
            size: "xl",
            color: "#ffffff"
          },
          {
            type: "text",
            text: "感謝您的訂購！",
            size: "sm",
            color: "#ffffff",
            margin: "sm"
          }
        ],
        backgroundColor: "#FF6B35",
        paddingAll: "20px"
      },
      body: {
        type: "box",
        layout: "vertical",
        contents: [
          // 訂單資訊
          {
            type: "box",
            layout: "vertical",
            contents: [
              {
                type: "text",
                text: "訂單資訊",
                weight: "bold",
                size: "md",
                color: "#333333"
              },
              {
                type: "box",
                layout: "vertical",
                contents: [
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "訂單編號",
                        size: "sm",
                        color: "#666666",
                        flex: 2
                      },
                      {
                        type: "text",
                        text: order.orderNumber,
                        size: "sm",
                        weight: "bold",
                        flex: 3,
                        align: "end"
                      }
                    ]
                  },
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "訂單時間",
                        size: "sm",
                        color: "#666666",
                        flex: 2
                      },
                      {
                        type: "text",
                        text: formatDateTime(order.createdAt),
                        size: "sm",
                        flex: 3,
                        align: "end"
                      }
                    ],
                    margin: "sm"
                  },
                  {
                    type: "box",
                    layout: "horizontal",
                    contents: [
                      {
                        type: "text",
                        text: "訂單類型",
                        size: "sm",
                        color: "#666666",
                        flex: 2
                      },
                      {
                        type: "text",
                        text: getOrderTypeText(order.orderType, order.scheduledTime),
                        size: "sm",
                        flex: 3,
                        align: "end"
                      }
                    ],
                    margin: "sm"
                  }
                ],
                margin: "md"
              }
            ]
          },
          {
            type: "separator",
            margin: "xl"
          },
          // 商品列表
          {
            type: "box",
            layout: "vertical",
            contents: [
              {
                type: "text",
                text: "訂購商品",
                weight: "bold",
                size: "md",
                color: "#333333"
              },
              {
                type: "box",
                layout: "vertical",
                contents: createItemsContent(order.items),
                margin: "md"
              }
            ],
            margin: "xl"
          },
          {
            type: "separator",
            margin: "xl"
          },
          // 總計
          {
            type: "box",
            layout: "horizontal",
            contents: [
              {
                type: "text",
                text: "總計",
                weight: "bold",
                size: "lg",
                color: "#333333"
              },
              {
                type: "text",
                text: `NT$${order.totalPrice}`,
                weight: "bold",
                size: "lg",
                color: "#FF6B35",
                align: "end"
              }
            ],
            margin: "xl"
          },
          // 備註
          ...(order.note ? [{
            type: "separator",
            margin: "xl"
          }, {
            type: "box",
            layout: "vertical",
            contents: [
              {
                type: "text",
                text: "備註",
                weight: "bold",
                size: "sm",
                color: "#666666"
              },
              {
                type: "text",
                text: order.note,
                size: "sm",
                wrap: true,
                margin: "sm"
              }
            ],
            margin: "xl"
          }] : [])
        ],
        paddingAll: "20px"
      },
      footer: {
        type: "box",
        layout: "vertical",
        contents: [
          {
            type: "button",
            action: {
              type: "uri",
              label: "查看訂單詳情",
              uri: `https://liff.line.me/2007460761-vMp0L0WA/orders`
            },
            style: "primary",
            color: "#FF6B35"
          }
        ],
        paddingAll: "20px"
      }
    }
  };

  return flexMessage;
}

/**
 * 發送 Flex Message 到 LINE
 */
export async function sendOrderConfirmationMessage(order: Order) {
  try {
    // 創建 Flex Message
    const flexMessage = createOrderConfirmationFlexMessage(order);

    // 發送訊息
    await sendMessages([flexMessage]);

    console.log('訂單確認訊息已發送');
    return true;
  } catch (error) {
    console.error('發送訂單確認訊息失敗:', error);
    return false;
  }
}
