(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},2093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},5362:(e,r,o)=>{"use strict";o.d(r,{default:()=>n});var s=o(2115),i=o(5695),l=o(4222),t=o(6681);function n(){let e=(0,i.useRouter)(),r=(0,i.usePathname)(),{isInitialized:o,isLoggedIn:n}=(0,l.x)(),{isFullyVerified:u,loading:a}=(0,t.h)();(0,s.useEffect)(()=>{o&&!a&&f()},[o,n,u,a,r]);let f=()=>{if(console.log("LiffRouter: 處理路由",{pathname:r,isLoggedIn:n,isFullyVerified:u}),!n&&"/"!==r){console.log("LiffRouter: 用戶未登入，跳轉到首頁"),e.push("/");return}if(n&&!u&&["/products","/orders","/cart"].includes(r)){console.log("LiffRouter: 用戶未完全驗證，跳轉到註冊頁面"),e.push("/register");return}switch(r){case"/products":n&&u?console.log("LiffRouter: 允許訪問產品頁面"):n&&!u?(console.log("LiffRouter: 需要驗證，跳轉到註冊頁面"),e.push("/register")):(console.log("LiffRouter: 需要登入，跳轉到首頁"),e.push("/"));break;case"/orders":n&&u?console.log("LiffRouter: 允許訪問訂單頁面"):n&&!u?(console.log("LiffRouter: 需要驗證，跳轉到註冊頁面"),e.push("/register")):(console.log("LiffRouter: 需要登入，跳轉到首頁"),e.push("/"));break;case"/cart":n&&u?console.log("LiffRouter: 允許訪問購物車頁面"):n&&!u?(console.log("LiffRouter: 需要驗證，跳轉到註冊頁面"),e.push("/register")):(console.log("LiffRouter: 需要登入，跳轉到首頁"),e.push("/"));break;case"/register":n||(console.log("LiffRouter: 註冊頁面需要登入，跳轉到首頁"),e.push("/"));break;case"/":console.log("LiffRouter: 在首頁，無需跳轉");break;default:console.log("LiffRouter: 未知路由",r)}};return null}},5695:(e,r,o)=>{"use strict";var s=o(8999);o.o(s,"usePathname")&&o.d(r,{usePathname:function(){return s.usePathname}}),o.o(s,"useRouter")&&o.d(r,{useRouter:function(){return s.useRouter}})},6681:(e,r,o)=>{"use strict";o.d(r,{h:()=>n});var s=o(2115),i=o(4222),l=o(2302),t=o(6720);let n=()=>{let{isInitialized:e,isLoggedIn:r,userProfile:o}=(0,i.x)(),[n,u]=(0,s.useState)({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!0,error:null}),a=async()=>{if(!e||!r||!o)return void u(e=>({...e,loading:!1,isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null}));try{if(u(e=>({...e,loading:!0,error:null})),!l.b.hasValidToken()&&(console.log("No valid JWT token, performing backend login..."),!await (0,t.UK)()))throw Error("Failed to obtain JWT token");let e=await l.A.user.getProfile();if(e&&e.id){let r=!!e.id,o=!!e.isVerified,s=!!e.lineVerified,i=r&&o&&s;u({isRegistered:r,isPhoneVerified:o,isLineVerified:s,isFullyVerified:i,userInfo:e,loading:!1,error:null})}else u({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!1,error:null})}catch(e){var s;console.error("檢查會員狀態失敗:",e),(null==(s=e.response)?void 0:s.status)===404?u({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!1,error:null}):u(r=>{var o,s;return{...r,loading:!1,error:(null==(s=e.response)||null==(o=s.data)?void 0:o.message)||"檢查會員狀態失敗"}})}};return(0,s.useEffect)(()=>{e&&a()},[e,r,o]),{...n,refreshUserStatus:()=>{a()}}}},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},8873:(e,r,o)=>{Promise.resolve().then(o.t.bind(o,2093,23)),Promise.resolve().then(o.t.bind(o,7735,23)),Promise.resolve().then(o.t.bind(o,347,23)),Promise.resolve().then(o.bind(o,5362)),Promise.resolve().then(o.bind(o,4222))}},e=>{var r=r=>e(e.s=r);e.O(0,[360,932,222,441,684,358],()=>r(8873)),_N_E=e.O()}]);