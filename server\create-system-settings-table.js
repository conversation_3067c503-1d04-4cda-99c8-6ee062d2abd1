const db = require('./models');

async function createSystemSettingsTable() {
  try {
    console.log('開始創建 system_settings 表...');
    
    // 檢查表是否已存在
    const [results] = await db.sequelize.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'cocodrink' 
      AND TABLE_NAME = 'system_settings'
    `);
    
    if (results.length > 0) {
      console.log('system_settings 表已存在，跳過創建');
      return;
    }
    
    // 創建 system_settings 表
    await db.sequelize.query(`
      CREATE TABLE system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        \`key\` VARCHAR(255) NOT NULL UNIQUE COMMENT '設定鍵名',
        value TEXT NOT NULL COMMENT '設定值',
        description TEXT COMMENT '設定描述',
        type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '值的類型',
        createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系統設定表'
    `);
    
    console.log('✓ system_settings 表創建成功');
    
    // 插入預設設定
    const defaultSettings = [
      {
        key: 'order_wait_timeout',
        value: '60',
        description: '訂單等待接單超時時間（秒）',
        type: 'number'
      },
      {
        key: 'enable_order_notifications',
        value: 'true',
        description: '是否啟用訂單即時通知',
        type: 'boolean'
      },
      {
        key: 'max_upload_file_size',
        value: '5',
        description: '最大上傳文件大小（MB）',
        type: 'number'
      }
    ];
    
    for (const setting of defaultSettings) {
      await db.sequelize.query(`
        INSERT INTO system_settings (\`key\`, value, description, type, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, NOW(), NOW())
      `, {
        replacements: [setting.key, setting.value, setting.description, setting.type]
      });
      console.log(`✓ 插入預設設定: ${setting.key}`);
    }
    
    console.log('system_settings 表創建和初始化完成！');
    
  } catch (error) {
    console.error('創建 system_settings 表失敗:', error);
  } finally {
    await db.sequelize.close();
  }
}

createSystemSettingsTable();
