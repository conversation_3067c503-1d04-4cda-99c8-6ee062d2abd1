(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{6681:(e,s,l)=>{"use strict";l.d(s,{h:()=>n});var t=l(2115),r=l(4222),a=l(2302),i=l(6720);let n=()=>{let{isInitialized:e,isLoggedIn:s,userProfile:l}=(0,r.x)(),[n,d]=(0,t.useState)({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!0,error:null}),c=async()=>{if(!e||!s||!l)return void d(e=>({...e,loading:!1,isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null}));try{if(d(e=>({...e,loading:!0,error:null})),!a.b.hasValidToken()&&(console.log("No valid JWT token, performing backend login..."),!await (0,i.UK)()))throw Error("Failed to obtain JWT token");let e=await a.A.user.getProfile();if(e&&e.id){let s=!!e.id,l=!!e.isVerified,t=!!e.lineVerified,r=s&&l&&t;d({isRegistered:s,isPhoneVerified:l,isLineVerified:t,isFullyVerified:r,userInfo:e,loading:!1,error:null})}else d({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!1,error:null})}catch(e){var t;console.error("檢查會員狀態失敗:",e),(null==(t=e.response)?void 0:t.status)===404?d({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!1,error:null}):d(s=>{var l,t;return{...s,loading:!1,error:(null==(t=e.response)||null==(l=t.data)?void 0:l.message)||"檢查會員狀態失敗"}})}};return(0,t.useEffect)(()=>{e&&c()},[e,s,l]),{...n,refreshUserStatus:()=>{c()}}}},8665:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>v});var t=l(5155),r=l(4222),a=l(6681),i=l(2115),n=l(6720);let d=()=>{let[e,s]=(0,i.useState)(!1),[l,t]=(0,i.useState)(!1);return{isModalOpen:e,isLoggingOut:l,showLogoutConfirm:()=>{s(!0)},hideLogoutConfirm:()=>{s(!1)},logoutDirectly:async()=>{t(!0);try{await (0,n.Bo)()}catch(e){console.error("Direct logout failed:",e),t(!1)}},confirmLogout:async()=>{t(!0);try{await (0,n.Bo)()}catch(e){console.error("Confirmed logout failed:",e),t(!1),s(!1)}}}};var c=l(6766),x=l(6874),o=l.n(x),m=l(7312),h=l(3311),u=l(4835),f=l(306),j=l(1976),g=l(1007),N=l(5339),b=l(6151),p=l(1243),w=l(4416);function y(e){let{isOpen:s,onClose:l,onConfirm:r}=e,[a,d]=(0,i.useState)(!1),c=async()=>{d(!0);try{r&&r(),await (0,n.Bo)()}catch(e){console.error("Logout error:",e),d(!1),l()}};return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-red-600 px-6 py-4 text-white",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-white/20 rounded-full flex items-center justify-center",children:(0,t.jsx)(p.A,{className:"w-4 h-4"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"確認登出"})]}),!a&&(0,t.jsx)("button",{onClick:l,className:"p-1 hover:bg-white/20 rounded-full transition-colors",children:(0,t.jsx)(w.A,{className:"w-5 h-5"})})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(u.A,{className:"w-8 h-8 text-red-500"})}),(0,t.jsx)("p",{className:"text-gray-700 text-lg mb-2",children:"您確定要登出嗎？"}),(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"登出後您需要重新登入才能使用會員功能"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)("button",{onClick:l,disabled:a,className:"flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"取消"}),(0,t.jsx)("button",{onClick:c,disabled:a,className:"flex-1 px-4 py-3 bg-red-500 text-white rounded-xl font-medium hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,t.jsx)("span",{children:"登出中..."})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"確認登出"})]})})]})]})]})}):null}function v(){let{isInitialized:e,isLoggedIn:s,userProfile:l,error:x,isDevelopmentMode:p}=(0,r.x)(),{isFullyVerified:w,loading:v}=(0,a.h)(),{isModalOpen:C,isLoggingOut:A,showLogoutConfirm:k,hideLogoutConfirm:V,confirmLogout:O}=d(),[L,F]=(0,i.useState)(!0);return((0,i.useEffect)(()=>{e&&!v&&F(!1)},[e,v]),p&&console.log("開發模式已啟用，繞過 LIFF 登入"),L)?(0,t.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(m.A,{className:"w-16 h-16 text-orange-500 mx-auto mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"absolute -top-2 -right-2",children:(0,t.jsx)(h.A,{className:"w-6 h-6 text-yellow-500 animate-bounce"})})]}),(0,t.jsx)("p",{className:"text-xl font-semibold text-orange-800 mb-2",children:"COCO 飲料店"}),(0,t.jsx)("p",{className:"text-orange-600",children:"正在為您準備最棒的飲品體驗..."})]})}):x?(0,t.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-3xl shadow-warm p-8 max-w-md w-full text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(m.A,{className:"w-8 h-8 text-red-500"})}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"哎呀！出了點問題"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:x.message}),(0,t.jsx)("button",{className:"w-full py-3 px-6 gradient-orange text-white font-semibold rounded-2xl btn-hover shadow-warm",onClick:()=>window.location.reload(),children:"重新載入"})]})}):(0,t.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,t.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 gradient-orange rounded-full flex items-center justify-center",children:(0,t.jsx)(m.A,{className:"w-6 h-6 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"COCO"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"飲料專門店"})]})]}),(0,t.jsx)("div",{children:s?(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[l&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[l.pictureUrl&&(0,t.jsx)(c.default,{src:l.pictureUrl,alt:"Profile",width:36,height:36,className:"rounded-full border-2 border-orange-200"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700 hidden sm:block",children:l.displayName})]}),(0,t.jsx)("button",{className:"p-2 bg-red-50 text-red-600 rounded-full hover:bg-red-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",onClick:k,disabled:A,title:A?"登出中...":"登出",children:A?(0,t.jsx)("div",{className:"w-5 h-5 border-2 border-red-600/30 border-t-red-600 rounded-full animate-spin"}):(0,t.jsx)(u.A,{className:"w-5 h-5"})})]}):(0,t.jsxs)("button",{className:"flex items-center gap-2 px-4 py-2 gradient-green text-white rounded-full btn-hover shadow-warm",onClick:n.Rf,children:[(0,t.jsx)(f.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"LINE 登入"})]})})]})}),(0,t.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,t.jsx)("div",{className:"max-w-md mx-auto",children:(0,t.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl shadow-warm p-8 mb-6 card-float",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsxs)("div",{className:"relative inline-block mb-4",children:[(0,t.jsx)("div",{className:"w-20 h-20 gradient-orange rounded-full flex items-center justify-center mx-auto",children:(0,t.jsx)(m.A,{className:"w-10 h-10 text-white"})}),(0,t.jsx)("div",{className:"absolute -top-1 -right-1",children:(0,t.jsx)(j.A,{className:"w-6 h-6 text-red-500 animate-pulse"})})]}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"歡迎來到 COCO"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:"最棒的手搖飲品體驗"})]}),s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"bg-orange-50 rounded-2xl p-4 mb-6 text-center",children:[(0,t.jsxs)("p",{className:"text-orange-800 font-medium",children:["嗨！",(null==l?void 0:l.displayName)||"親愛的顧客"," \uD83D\uDC4B"]}),(0,t.jsx)("p",{className:"text-orange-600 text-sm mt-1",children:"準備好享受美味飲品了嗎？"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[!w&&(0,t.jsxs)(o(),{href:"/register",className:"flex items-center justify-between w-full py-4 px-6 bg-blue-500 text-white rounded-2xl btn-hover shadow-warm group",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(g.A,{className:"w-6 h-6"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("p",{className:"font-semibold",children:"會員註冊/驗證"}),(0,t.jsx)("p",{className:"text-blue-100 text-sm",children:"享受會員專屬優惠"})]})]}),(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center group-hover:bg-blue-300 transition-colors",children:(0,t.jsx)("span",{className:"text-white text-sm",children:"→"})})]}),w?(0,t.jsxs)(o(),{href:"/products",className:"flex items-center justify-between w-full py-4 px-6 gradient-green text-white rounded-2xl btn-hover shadow-warm group",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(m.A,{className:"w-6 h-6"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("p",{className:"font-semibold",children:"開始點餐"}),(0,t.jsx)("p",{className:"text-green-100 text-sm",children:"探索美味飲品"})]})]}),(0,t.jsx)("div",{className:"w-8 h-8 bg-green-400 rounded-full flex items-center justify-center group-hover:bg-green-300 transition-colors",children:(0,t.jsx)("span",{className:"text-white text-sm",children:"→"})})]}):(0,t.jsxs)("div",{className:"flex items-center justify-between w-full py-4 px-6 bg-gray-300 text-gray-500 rounded-2xl cursor-not-allowed",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(m.A,{className:"w-6 h-6"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("p",{className:"font-semibold",children:"開始點餐"}),(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"請先完成會員註冊"})]})]}),(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center",children:(0,t.jsx)(N.A,{className:"w-4 h-4 text-gray-500"})})]}),w?(0,t.jsxs)(o(),{href:"/orders",className:"flex items-center justify-between w-full py-4 px-6 gradient-purple text-white rounded-2xl btn-hover shadow-warm group",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(b.A,{className:"w-6 h-6"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("p",{className:"font-semibold",children:"我的訂單"}),(0,t.jsx)("p",{className:"text-purple-100 text-sm",children:"查看訂單狀態"})]})]}),(0,t.jsx)("div",{className:"w-8 h-8 bg-purple-400 rounded-full flex items-center justify-center group-hover:bg-purple-300 transition-colors",children:(0,t.jsx)("span",{className:"text-white text-sm",children:"→"})})]}):(0,t.jsxs)("div",{className:"flex items-center justify-between w-full py-4 px-6 bg-gray-300 text-gray-500 rounded-2xl cursor-not-allowed",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(b.A,{className:"w-6 h-6"}),(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("p",{className:"font-semibold",children:"我的訂單"}),(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"請先完成會員註冊"})]})]}),(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center",children:(0,t.jsx)(N.A,{className:"w-4 h-4 text-gray-500"})})]})]})]}):(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"bg-yellow-50 rounded-2xl p-6 mb-6",children:[(0,t.jsx)(h.A,{className:"w-12 h-12 text-yellow-500 mx-auto mb-3"}),(0,t.jsx)("p",{className:"text-gray-700 font-medium mb-2",children:"開始您的美味之旅"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"請先登入以使用線上點餐系統"})]}),(0,t.jsxs)("button",{className:"w-full py-4 px-6 gradient-green text-white font-semibold rounded-2xl btn-hover shadow-warm flex items-center justify-center gap-3",onClick:n.Rf,children:[(0,t.jsx)(f.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"使用 LINE 登入"})]})]})]})})}),(0,t.jsx)("footer",{className:"bg-white/60 backdrop-blur-sm border-t border-orange-100 py-6",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(0,t.jsx)(m.A,{className:"w-5 h-5 text-orange-500"}),(0,t.jsx)("span",{className:"font-semibold text-gray-700",children:"COCO 飲料店"})]}),(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"\xa9 2024 COCO飲料店. All rights reserved."}),(0,t.jsx)("p",{className:"text-gray-400 text-xs mt-1",children:"用心調製每一杯飲品 ❤️"})]})}),(0,t.jsx)(y,{isOpen:C,onClose:V,onConfirm:O})]})}},8914:(e,s,l)=>{Promise.resolve().then(l.bind(l,8665))}},e=>{var s=s=>e(e.s=s);e.O(0,[932,659,685,222,441,684,358],()=>s(8914)),_N_E=e.O()}]);