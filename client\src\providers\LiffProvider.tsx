'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import liff from '@line/liff';
import { initializeLiff, isLoggedIn, getUserProfile } from '@/utils/liff';

// LIFF 上下文類型
interface LiffContextType {
  isInitialized: boolean;
  isLoggedIn: boolean;
  isInClient: boolean;
  userProfile: any | null;
  error: Error | null;
  isDevelopmentMode?: boolean; // 新增開發模式標記
}

// 創建 LIFF 上下文
const LiffContext = createContext<LiffContextType>({
  isInitialized: false,
  isLoggedIn: false,
  isInClient: false,
  userProfile: null,
  error: null,
  isDevelopmentMode: false,
});

// 開發模式的模擬用戶資料
const mockUserProfile = {
  userId: 'Udf723d52cde2495367205e5751fb8c8d',
  displayName: 'Andy瑄',
  pictureUrl: 'https://profile.line-scdn.net/0hhnplyM1hN0FdFR30vaZJPi1FNCt-ZG5TIyRxIjgdaSI3IyMVI3svdGwRYCQzJ3AUI3V_LzoXa3FRBkAnQ0PLdVolanBhI3UQcHV_pw',
};

// LIFF 提供者組件
export const LiffProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 判斷是否為開發環境
  const isDevelopment = process.env.NODE_ENV === 'development';
  const skipLiffLogin = isDevelopment && process.env.NEXT_PUBLIC_SKIP_LIFF_LOGIN === 'true';

  const [isInitialized, setIsInitialized] = useState(skipLiffLogin);
  const [isInClient, setIsInClient] = useState(false);
  const [userProfile, setUserProfile] = useState<any | null>(skipLiffLogin ? mockUserProfile : null);
  const [error, setError] = useState<Error | null>(null);

  console.log('LiffProvider: isDevelopment =', isDevelopment);
  console.log('LiffProvider: NEXT_PUBLIC_SKIP_LIFF_LOGIN =', process.env.NEXT_PUBLIC_SKIP_LIFF_LOGIN);
  console.log('LiffProvider: skipLiffLogin =', skipLiffLogin);
  console.log('LiffProvider: isInitialized =', isInitialized);

  useEffect(() => {
    // 如果是開發模式且想要繞過 LIFF 登入，直接返回
    if (skipLiffLogin) {
      console.log('開發模式：已繞過 LIFF 登入');
      setIsInitialized(true);
      setUserProfile(mockUserProfile);
      return;
    }

    // 確保只在客戶端執行 LIFF 初始化
    if (typeof window !== 'undefined') {
      // 初始化 LIFF
      const initLiff = async () => {
        try {
          await initializeLiff();
          setIsInitialized(true);

          // 檢查是否在 LINE 應用內
          const inClient = liff.isInClient();
          console.log("inClient", inClient);
          setIsInClient(inClient);
          console.log("isLoggedIn", liff.isLoggedIn());
          // 如果用戶已登入，獲取用戶資料
          if (liff.isLoggedIn()) {
            const profile = await getUserProfile();
            console.log("getUserProfile profile", profile);
            setUserProfile(profile);
          }
        } catch (err) {
          console.error('LIFF initialization failed', err);
          setError(err instanceof Error ? err : new Error('Failed to initialize LIFF'));
        }
      };

      initLiff();
    }
  }, [skipLiffLogin]);

  // 提供 LIFF 上下文
  const value = {
    isInitialized,
    isLoggedIn: skipLiffLogin
      ? true
      : (typeof window !== 'undefined' ? isLoggedIn() : false),
    isInClient,
    userProfile,
    error,
    isDevelopmentMode: skipLiffLogin,
  };

  console.log('LiffProvider value:', value);

  return <LiffContext.Provider value={value}>{children}</LiffContext.Provider>;
};

// 使用 LIFF 上下文的 Hook
export const useLiff = () => useContext(LiffContext);

export default LiffProvider;




