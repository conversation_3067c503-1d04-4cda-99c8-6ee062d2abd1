exports.id=947,exports.ids=[947],exports.modules={147:(e,o,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},584:(e,o,t)=>{"use strict";t.d(o,{default:()=>l,x:()=>s});var r=t(687),n=t(3210);t(9010),t(2013);let i=(0,n.createContext)({isInitialized:!1,isLoggedIn:!1,isInClient:!1,userProfile:null,error:null,isDevelopmentMode:!1}),s=()=>(0,n.useContext)(i),l=({children:e})=>{let[o,t]=(0,n.useState)(!1),[s,l]=(0,n.useState)(!1),[a,c]=(0,n.useState)(null),[d,u]=(0,n.useState)(null);return(0,n.useEffect)(()=>{},[!1]),(0,r.jsx)(i.Provider,{value:{isInitialized:o,isLoggedIn:!1,isInClient:s,userProfile:a,error:d,isDevelopmentMode:!1},children:e})}},923:(e,o,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},1135:()=>{},1358:(e,o,t)=>{"use strict";t.d(o,{default:()=>n});var r=t(2907);(0,r.registerClientReference)(function(){throw Error("Attempted to call LiffProvider() from the server but LiffProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\providers\\LiffProvider.tsx","LiffProvider"),(0,r.registerClientReference)(function(){throw Error("Attempted to call useLiff() from the server but useLiff is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\providers\\LiffProvider.tsx","useLiff");let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\0_project\\\\augment-projects\\\\cocodrink-line\\\\client\\\\src\\\\providers\\\\LiffProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\providers\\LiffProvider.tsx","default")},2013:(e,o,t)=>{"use strict";t.d(o,{UK:()=>p,VM:()=>u,Rf:()=>g,Bo:()=>v,bU:()=>y});var r=t(9010),n=t(7327);let i=[{accessToken:"dev_token_andy",profile:{userId:"Udf723d52cde2495367205e5751fb8c8d",displayName:"Andy瑄",pictureUrl:"https://profile.line-scdn.net/0hhnplyM1hN0FdFR30vaZJPi1FNCt-ZG5TIyRxIjgdaSI3IyMVI3svdGwRYCQzJ3AUI3V_LzoXa3FRBkAnQ0PLdVolanBhI3UQcHV_pw"}},{accessToken:"dev_token_1",profile:{userId:"U1234567890abcdef1234567890abcdef",displayName:"測試用戶一",pictureUrl:"https://profile.line-scdn.net/0h1234567890abcdef_large"}},{accessToken:"dev_token_2",profile:{userId:"U2345678901bcdef12345678901bcdef1",displayName:"測試用戶二",pictureUrl:"https://profile.line-scdn.net/0h2345678901bcdef_large"}},{accessToken:"dev_token_3",profile:{userId:"U3456789012cdef123456789012cdef12",displayName:"測試用戶三",pictureUrl:null}}],s={isLoggedIn:!1,currentUser:null,isInClient:!0},l={init:async e=>(console.log("\uD83D\uDD27 Dev Mode: LIFF initialized with config:",e),Promise.resolve()),isLoggedIn:()=>s.isLoggedIn,isInClient:()=>s.isInClient,login:(e=0)=>{let o=i[e]||i[0];s.isLoggedIn=!0,s.currentUser=o,console.log("\uD83D\uDD27 Dev Mode: User logged in:",o.profile.displayName),localStorage.setItem("dev_liff_user",JSON.stringify(o)),localStorage.setItem("dev_liff_logged_in","true")},logout:()=>{s.isLoggedIn=!1,s.currentUser=null,console.log("\uD83D\uDD27 Dev Mode: User logged out"),localStorage.removeItem("dev_liff_user"),localStorage.removeItem("dev_liff_logged_in")},getAccessToken:()=>s.isLoggedIn&&s.currentUser?s.currentUser.accessToken:null,getProfile:async()=>{if(!s.isLoggedIn||!s.currentUser)throw Error("User is not logged in");return s.currentUser.profile},getIDToken:()=>s.isLoggedIn?"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FjY2Vzcy5saW5lLm1lIiwic3ViIjoiVTEyMzQ1Njc4OTBhYmNkZWYxMjM0NTY3ODkwYWJjZGVmIiwiYXVkIjoiMTIzNDU2Nzg5MCIsImV4cCI6MTYzMjQ2NzIwMCwiaWF0IjoxNjMyNDYzNjAwLCJuYW1lIjoi5ris6Kmm55So5oi25LiAIiwicGljdHVyZSI6Imh0dHBzOi8vcHJvZmlsZS5saW5lLXNjZG4ubmV0LzBoMTIzNDU2Nzg5MGFiY2RlZl9sYXJnZSJ9.mock_signature":null,closeWindow:()=>{console.log("\uD83D\uDD27 Dev Mode: Close window called")},restoreState:()=>{let e=localStorage.getItem("dev_liff_user"),o="true"===localStorage.getItem("dev_liff_logged_in");e&&o&&(s.currentUser=JSON.parse(e),s.isLoggedIn=!0,console.log("\uD83D\uDD27 Dev Mode: State restored for user:",s.currentUser.profile.displayName))},switchUser:e=>{e>=0&&e<i.length&&(l.login(e),window.location.reload())},getAvailableUsers:()=>i.map((e,o)=>({index:o,name:e.profile.displayName,userId:e.profile.userId}))},a=async(e=0)=>{try{l.login(e);let o=l.getAccessToken();if(!o)throw Error("Unable to get access token");let t=await n.A.auth.lineLogin(o);return console.log("\uD83D\uDD27 Dev Mode: Backend login successful:",t),t}catch(e){throw console.error("\uD83D\uDD27 Dev Mode: Backend login failed:",e),e}},c=()=>!1,d=()=>!1,u=async()=>{try{if(c()){if(!l.isLoggedIn())throw Error("User is not logged in");let e=await l.getProfile();return console.log("profile",e),{userId:e.userId,displayName:e.displayName,pictureUrl:e.pictureUrl}}if(!r.A.isLoggedIn())throw Error("User is not logged in");let t=await r.A.getProfile();console.log("profile:",t);var e=r.A.getDecodedIDToken();console.log("user:",e);var o=e?.email;return console.log("email:",o),{userId:t.userId,displayName:t.displayName,pictureUrl:t.pictureUrl,email:o}}catch(e){throw console.error("Failed to get user profile",e),e}},g=()=>{r.A.isLoggedIn()||r.A.login()},f=async(e=0)=>{try{if(c())return await a(e);if(!r.A.isLoggedIn())throw Error("User is not logged in to LINE");let o=I();if(!o)throw Error("Unable to get access token");let t=await n.A.auth.lineLogin(o);return console.log("Backend login successful:",t),t}catch(e){throw console.error("Backend login failed:",e),e}},p=async()=>{try{if(n.b.hasValidToken())return console.log("Valid JWT token found, no need to login"),!0;if(!d())return console.log("User not logged in to LIFF"),!1;return console.log("LIFF logged in but no JWT token, performing backend login..."),await f(),console.log("Backend login completed successfully"),!0}catch(e){return console.error("Auto backend login failed:",e),!1}},h=()=>{try{["userProfile","userPreferences","cartItems","lastOrderId","userSettings"].forEach(e=>{localStorage.removeItem(e)}),sessionStorage.clear(),console.log("User data cleared successfully")}catch(e){console.error("Error clearing user data:",e)}},m=()=>{try{["access_token","refresh_token","id_token","liff_token","auth_token"].forEach(e=>{localStorage.removeItem(e),sessionStorage.removeItem(e)}),console.log("Auth tokens cleared successfully")}catch(e){console.error("Error clearing auth tokens:",e)}},v=async()=>{try{if(!r.A.isLoggedIn())return void console.warn("User is not logged in");try{I()&&(await n.A.auth.logout(),console.log("Server logout successful"))}catch(e){console.warn("Server logout failed, continuing with client logout:",e)}h(),m(),r.A.logout(),setTimeout(()=>{window.location.href="/"},100)}catch(e){console.error("Logout failed:",e),window.location.reload()}},I=()=>null,y=async e=>{console.error("sendMessages is only available in browser environment")}},4431:(e,o,t)=>{"use strict";t.r(o),t.d(o,{default:()=>u,metadata:()=>d});var r=t(7413),n=t(2376),i=t.n(n),s=t(8726),l=t.n(s);t(1135);var a=t(1358),c=t(5096);let d={title:"橘子工坊飲料店 - 線上點餐系統",description:"橘子工坊飲料店線上點餐系統，提供會員註冊、手機驗證、線上點餐和預約點餐功能。"};function u({children:e}){return(0,r.jsx)("html",{lang:"zh-TW",children:(0,r.jsx)("body",{className:`${i().variable} ${l().variable} antialiased`,children:(0,r.jsxs)(a.default,{children:[(0,r.jsx)(c.default,{}),e]})})})}},5096:(e,o,t)=>{"use strict";t.d(o,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\0_project\\\\augment-projects\\\\cocodrink-line\\\\client\\\\src\\\\components\\\\LiffRouter.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\components\\LiffRouter.tsx","default")},6311:(e,o,t)=>{Promise.resolve().then(t.bind(t,5096)),Promise.resolve().then(t.bind(t,1358))},7327:(e,o,t)=>{"use strict";t.d(o,{A:()=>a,b:()=>i});var r=t(1060);let n="https://orangedrink-api2.zeabur.app/api";console.log("API_BASE_URL:",n);let i={setToken:e=>{},getToken:()=>null,removeToken:()=>{},hasValidToken:()=>{let e=i.getToken();if(!e)return!1;try{let o=e.split(".");if(3!==o.length)return!1;let t=JSON.parse(atob(o[1])),r=Math.floor(Date.now()/1e3);return!t.exp||t.exp>r}catch(e){return!1}}},s=r.A.create({baseURL:n,headers:{"Content-Type":"application/json"}}),l=r.A.create({baseURL:n,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let o=i.getToken();return console.log("JWT Token:",o?`${o.substring(0,20)}...`:"None"),o&&(e.headers.Authorization=`Bearer ${o}`),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(console.error("Unauthorized, please login again"),i.removeToken()),Promise.reject(e)));let a={auth:{lineLogin:async e=>{let o=await l.post("/auth/line-login",{accessToken:e});return console.log("publicApi.post:","/auth/line-login"),console.log("publicApi.post accessToken:",e),console.log("LINE login response:",o.data),o.data.token&&(i.setToken(o.data.token),console.log("JWT token saved successfully")),o.data},register:async(e,o,t)=>(await l.post("/auth/register",{lineId:e,name:o,phone:t})).data,sendVerification:async e=>(await l.post("/auth/send-verification",{phone:e})).data,verifyPhone:async(e,o)=>(await l.post("/auth/verify-phone",{phone:e,code:o})).data,logout:async()=>{let e=await s.post("/auth/logout");return i.removeToken(),console.log("JWT token removed"),e.data},getUserLogs:async(e=1,o=20,t)=>{let r=new URLSearchParams({page:e.toString(),limit:o.toString()});return t&&r.append("action",t),(await s.get(`/auth/logs?${r}`)).data}},user:{getProfile:async()=>(await s.get("/user/profile")).data,updateProfile:async e=>(await s.put("/user/profile",e)).data,getOrders:async()=>(await s.get("/user/orders")).data},products:{getAll:async()=>(await s.get("/products")).data,getCategories:async()=>(await s.get("/products/categories")).data,getByCategory:async e=>(await s.get(`/products/category/${e}`)).data,getById:async e=>(await s.get(`/products/${e}`)).data},orders:{create:async e=>(await s.post("/orders",e)).data,getById:async e=>(await s.get(`/orders/${e}`)).data,cancel:async e=>(await s.put(`/orders/${e}/cancel`)).data,requestCancel:async(e,o)=>(await s.post(`/orders/${e}/request-cancel`,{reason:o})).data}}},7614:(e,o,t)=>{"use strict";t.d(o,{default:()=>s}),t(3210);var r=t(6189),n=t(584),i=t(7979);function s(){(0,r.useRouter)(),(0,r.usePathname)();let{isInitialized:e,isLoggedIn:o}=(0,n.x)(),{isFullyVerified:t,loading:s}=(0,i.h)();return null}},7979:(e,o,t)=>{"use strict";t.d(o,{h:()=>l});var r=t(3210),n=t(584),i=t(7327),s=t(2013);let l=()=>{let{isInitialized:e,isLoggedIn:o,userProfile:t}=(0,n.x)(),[l,a]=(0,r.useState)({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!0,error:null}),c=async()=>{if(!e||!o||!t)return void a(e=>({...e,loading:!1,isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null}));try{if(a(e=>({...e,loading:!0,error:null})),!i.b.hasValidToken()&&(console.log("No valid JWT token, performing backend login..."),!await (0,s.UK)()))throw Error("Failed to obtain JWT token");let e=await i.A.user.getProfile();if(e&&e.id){let o=!!e.id,t=!!e.isVerified,r=!!e.lineVerified,n=o&&t&&r;a({isRegistered:o,isPhoneVerified:t,isLineVerified:r,isFullyVerified:n,userInfo:e,loading:!1,error:null})}else a({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!1,error:null})}catch(e){console.error("檢查會員狀態失敗:",e),e.response?.status===404?a({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!1,error:null}):a(o=>({...o,loading:!1,error:e.response?.data?.message||"檢查會員狀態失敗"}))}};return(0,r.useEffect)(()=>{e&&c()},[e,o,t]),{...l,refreshUserStatus:()=>{c()}}}},9455:(e,o,t)=>{Promise.resolve().then(t.bind(t,7614)),Promise.resolve().then(t.bind(t,584))}};