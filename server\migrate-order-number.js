const db = require('./models');

async function migrateOrderNumber() {
  try {
    console.log('開始遷移 orderNumber 欄位...');
    
    // 檢查 orderNumber 欄位是否已存在
    const [results] = await db.sequelize.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'cocodrink' 
      AND TABLE_NAME = 'orders' 
      AND COLUMN_NAME = 'orderNumber'
    `);
    
    if (results.length > 0) {
      console.log('orderNumber 欄位已存在，跳過遷移');
      return;
    }
    
    // 添加 orderNumber 欄位
    await db.sequelize.query(`
      ALTER TABLE orders 
      ADD COLUMN orderNumber VARCHAR(255) UNIQUE AFTER id
    `);
    console.log('✓ 已添加 orderNumber 欄位');
    
    // 為現有訂單生成 orderNumber
    const orders = await db.orders.findAll({
      order: [['createdAt', 'ASC']]
    });
    
    console.log(`找到 ${orders.length} 筆現有訂單，開始生成訂單編號...`);
    
    for (let i = 0; i < orders.length; i++) {
      const order = orders[i];
      const createdDate = new Date(order.createdAt);
      const dateStr = createdDate.toISOString().slice(0, 10).replace(/-/g, '');
      const orderSequence = String(i + 1).padStart(3, '0');
      const orderNumber = `ORD-${dateStr}-${orderSequence}`;
      
      await order.update({ orderNumber });
      console.log(`✓ 訂單 ${order.id} 已設置編號: ${orderNumber}`);
    }
    
    // 設置 orderNumber 為 NOT NULL
    await db.sequelize.query(`
      ALTER TABLE orders 
      MODIFY COLUMN orderNumber VARCHAR(255) NOT NULL
    `);
    console.log('✓ 已設置 orderNumber 為 NOT NULL');
    
    console.log('遷移完成！');
    
  } catch (error) {
    console.error('遷移失敗:', error);
  } finally {
    await db.sequelize.close();
  }
}

migrateOrderNumber();
