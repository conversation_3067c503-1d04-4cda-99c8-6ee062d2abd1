-- 添加 orderNumber 欄位到 orders 表
-- 執行日期: 2024-12-01

USE cocodrink;

-- 添加 orderNumber 欄位
ALTER TABLE orders 
ADD COLUMN orderNumber VARCHAR(255) UNIQUE AFTER id,
ADD COMMENT ON COLUMN orderNumber '訂單編號，格式：ORD-YYYYMMDD-XXX';

-- 為現有訂單生成 orderNumber
-- 注意：這個腳本會為現有的訂單生成訂單編號
SET @counter = 0;
UPDATE orders 
SET orderNumber = CONCAT('ORD-', DATE_FORMAT(createdAt, '%Y%m%d'), '-', LPAD((@counter := @counter + 1), 3, '0'))
WHERE orderNumber IS NULL
ORDER BY createdAt;

-- 設置 orderNumber 為 NOT NULL
ALTER TABLE orders 
MODIFY COLUMN orderNumber VARCHAR(255) NOT NULL;

-- 驗證結果
SELECT COUNT(*) as total_orders, 
       COUNT(orderNumber) as orders_with_number 
FROM orders;

-- 顯示前幾筆訂單的編號
SELECT id, orderNumber, createdAt 
FROM orders 
ORDER BY createdAt 
LIMIT 10;
