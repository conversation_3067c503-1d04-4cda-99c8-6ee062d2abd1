module.exports = app => {
  const orderController = require('../controllers/order.controller');
  const { authJwt } = require('../middleware');
  const router = require('express').Router();

  // 創建新訂單
  router.post('/', [authJwt.verifyToken], orderController.createOrder);

  // 獲取訂單詳情
  router.get('/:id', [authJwt.verifyToken], orderController.getOrderById);

  // 取消訂單
  router.put('/:id/cancel', [authJwt.verifyToken], orderController.cancelOrder);

  // 重新送單
  router.put('/:id/resubmit', [authJwt.verifyToken], orderController.resubmitOrder);

  app.use('/api/orders', router);
};
