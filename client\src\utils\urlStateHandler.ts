/**
 * LIFF 狀態參數處理工具
 * 專門處理 LINE LIFF 登入後的路由跳轉
 */

export class UrlStateHandler {
  private static instance: UrlStateHandler;
  private pendingRoute: string | null = null;
  private hasProcessed: boolean = false;
  private readonly STORAGE_KEY = 'url_pending_route';

  private constructor() {
    // 頁面載入時檢查是否有待處理的路由
    this.loadPendingRoute();
  }

  public static getInstance(): UrlStateHandler {
    if (!UrlStateHandler.instance) {
      UrlStateHandler.instance = new UrlStateHandler();
    }
    return UrlStateHandler.instance;
  }

  /**
   * 保存當前路徑作為待處理路由
   */
  public saveCurrentRoute(): void {
    if (typeof window === 'undefined') return;
    
    try {
      // 獲取當前路徑
      const currentPath = window.location.pathname + window.location.search;
      
      // 排除登入頁面和註冊頁面
      if (currentPath === '/' || currentPath === '/register') {
        return;
      }
      
      // 保存到 localStorage
      localStorage.setItem(this.STORAGE_KEY, currentPath);
      console.log('UrlStateHandler: 已保存當前路徑:', currentPath);
    } catch (error) {
      console.error('UrlStateHandler: 保存路徑失敗:', error);
    }
  }

  /**
   * 從 localStorage 載入待處理的路由
   */
  private loadPendingRoute(): void {
    if (typeof window === 'undefined') return;
    
    try {
      const savedRoute = localStorage.getItem(this.STORAGE_KEY);
      if (savedRoute) {
        this.pendingRoute = savedRoute;
        console.log('UrlStateHandler: 載入待處理路由:', savedRoute);
      }
    } catch (error) {
      console.error('UrlStateHandler: 載入路由失敗:', error);
    }
  }

  /**
   * 獲取待處理的路由
   */
  public getPendingRoute(): string | null {
    return this.pendingRoute;
  }

  /**
   * 標記為已處理
   */
  public markAsProcessed(): void {
    this.hasProcessed = true;
    this.pendingRoute = null;
    
    // 清除 localStorage 中的記錄
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.STORAGE_KEY);
    }
  }

  /**
   * 檢查是否已處理
   */
  public isProcessed(): boolean {
    return this.hasProcessed;
  }

  /**
   * 重置狀態
   */
  public reset(): void {
    this.pendingRoute = null;
    this.hasProcessed = false;
  }

  /**
   * 處理路由跳轉
   */
  public handleRouting(router: any): boolean {
    if (this.hasProcessed || !this.pendingRoute) {
      return false;
    }

    console.log('UrlStateHandler: 執行路由跳轉到:', this.pendingRoute);
    router.push(this.pendingRoute);
    this.markAsProcessed();
    return true;
  }
}

// 導出單例實例
export const urlStateHandler = UrlStateHandler.getInstance();

