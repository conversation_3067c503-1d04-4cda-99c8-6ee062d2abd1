"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[142],{1352:(e,t,i)=>{i.d(t,{A:()=>o});var s=i(5155),r=i(2115),a=i(4416),n=i(7712),l=i(4616);function o(e){let{product:t,isOpen:i,onClose:o,onAddToCart:d,isEditMode:c=!1,currentCustomizations:m}=e,[u,x]=(0,r.useState)(1),[h,p]=(0,r.useState)({sugar:"",ice:"",toppings:[]});(0,r.useEffect)(()=>{if(t.options&&i){var e,s,r,a,n,l,o,d;let i=t.options.filter(e=>"sugar"===e.optionType),u=t.options.filter(e=>"ice"===e.optionType);c&&m?p({sugar:m.sugar||(null==(e=i.find(e=>"正常糖"===e.optionName))?void 0:e.optionName)||(null==(s=i[0])?void 0:s.optionName)||"",ice:m.ice||(null==(r=u.find(e=>"正常冰"===e.optionName))?void 0:r.optionName)||(null==(a=u[0])?void 0:a.optionName)||"",toppings:m.toppings||[]}):p({sugar:(null==(n=i.find(e=>"正常糖"===e.optionName))?void 0:n.optionName)||(null==(l=i[0])?void 0:l.optionName)||"",ice:(null==(o=u.find(e=>"正常冰"===e.optionName))?void 0:o.optionName)||(null==(d=u[0])?void 0:d.optionName)||"",toppings:[]}),x(1)}},[t,i,c,m]);let g=()=>{let e=0;return t.options&&h.toppings.forEach(i=>{let s=t.options.find(e=>"topping"===e.optionType&&e.optionName===i);s&&(e+=s.additionalPrice)}),(t.price+e)*u},f=(e,t)=>{"topping"===e?p(e=>({...e,toppings:e.toppings.includes(t)?e.toppings.filter(e=>e!==t):[...e.toppings,t]})):p(i=>({...i,[e]:t}))},b=e=>{var i;return(null==(i=t.options)?void 0:i.filter(t=>t.optionType===e))||[]};return i?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-warm max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"sticky top-0 bg-white rounded-t-3xl border-b border-gray-100 p-6 flex items-center justify-between",children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"客製化選項"}),(0,s.jsx)("button",{onClick:o,className:"p-2 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors",children:(0,s.jsx)(a.A,{className:"w-5 h-5"})})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:t.name}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:t.description}),(0,s.jsxs)("p",{className:"text-orange-500 font-bold",children:["基本價格: NT$ ",t.price]})]}),b("sugar").length>0&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-800 mb-3",children:"甜度"}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-2",children:b("sugar").map(e=>(0,s.jsx)("button",{onClick:()=>f("sugar",e.optionName),className:"p-3 rounded-xl border-2 transition-colors ".concat(h.sugar===e.optionName?"border-orange-500 bg-orange-50 text-orange-700":"border-gray-200 hover:border-orange-300"),children:e.optionName},e.id))})]}),b("ice").length>0&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-800 mb-3",children:"冰量"}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-2",children:b("ice").map(e=>(0,s.jsx)("button",{onClick:()=>f("ice",e.optionName),className:"p-3 rounded-xl border-2 transition-colors ".concat(h.ice===e.optionName?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-200 hover:border-blue-300"),children:e.optionName},e.id))})]}),b("topping").length>0&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-800 mb-3",children:"配件加料"}),(0,s.jsx)("div",{className:"space-y-2",children:b("topping").map(e=>(0,s.jsxs)("button",{onClick:()=>f("topping",e.optionName),className:"w-full p-3 rounded-xl border-2 transition-colors flex items-center justify-between ".concat(h.toppings.includes(e.optionName)?"border-green-500 bg-green-50 text-green-700":"border-gray-200 hover:border-green-300"),children:[(0,s.jsx)("span",{children:e.optionName}),(0,s.jsx)("span",{className:"text-sm",children:e.additionalPrice>0?"+NT$ ".concat(e.additionalPrice):"免費"})]},e.id))})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-800 mb-3",children:"數量"}),(0,s.jsxs)("div",{className:"flex items-center justify-center gap-4",children:[(0,s.jsx)("button",{onClick:()=>x(Math.max(1,u-1)),className:"p-2 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors",children:(0,s.jsx)(n.A,{className:"w-5 h-5"})}),(0,s.jsx)("span",{className:"text-xl font-semibold w-12 text-center",children:u}),(0,s.jsx)("button",{onClick:()=>x(u+1),className:"p-2 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors",children:(0,s.jsx)(l.A,{className:"w-5 h-5"})})]})]}),(0,s.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 rounded-xl",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"font-semibold text-gray-800",children:"總計"}),(0,s.jsxs)("span",{className:"text-2xl font-bold text-orange-500",children:["NT$ ",g()]})]}),g()>t.price*u&&(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["包含配件加價 NT$ ",g()-t.price*u]})]}),(0,s.jsxs)("button",{onClick:()=>{d(t,h,g()),o()},className:"w-full py-4 px-6 gradient-orange text-white rounded-2xl font-semibold btn-hover shadow-warm",children:[c?"確認修改":"加入購物車"," - NT$ ",g()]})]})]})}):null}},3841:(e,t,i)=>{i.d(t,{_:()=>r});var s=i(2115);let r=()=>{let[e,t]=(0,s.useState)([]),[i,r]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{t(JSON.parse(e))}catch(e){console.error("Failed to load cart from localStorage:",e)}},[]),(0,s.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(e))},[e]);let a=e=>{t(t=>t.filter(t=>t.id!==e))},n=()=>e.reduce((e,t)=>e+t.totalPrice*t.quantity,0),l=0===e.length;return{cartItems:e,isLoading:i,addToCart:(e,i,s)=>{let r=s||e.price,a={id:Date.now(),productId:e.id,name:e.name,basePrice:e.price,totalPrice:r,quantity:1,image:e.image,customizations:i};t(t=>{let s=t.findIndex(t=>t.productId===e.id&&JSON.stringify(t.customizations)===JSON.stringify(i));if(!(s>-1))return[...t,a];{let e=[...t];return e[s].quantity+=1,e}})},updateQuantity:(e,i)=>{if(i<=0)return void a(e);t(t=>t.map(t=>t.id===e?{...t,quantity:i}:t))},removeFromCart:a,clearCart:()=>{t([])},getTotalPrice:n,getTotalQuantity:()=>e.reduce((e,t)=>e+t.quantity,0),isEmpty:l,formatOrderData:(t,i,s)=>({items:e.map(e=>({productId:e.productId,quantity:e.quantity,price:e.totalPrice,customizations:e.customizations||{}})),totalPrice:n(),orderType:t,scheduledTime:i,note:s})}}},4416:(e,t,i)=>{i.d(t,{A:()=>s});let s=(0,i(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4616:(e,t,i)=>{i.d(t,{A:()=>s});let s=(0,i(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5339:(e,t,i)=>{i.d(t,{A:()=>s});let s=(0,i(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,t,i)=>{var s=i(8999);i.o(s,"usePathname")&&i.d(t,{usePathname:function(){return s.usePathname}}),i.o(s,"useRouter")&&i.d(t,{useRouter:function(){return s.useRouter}})},6681:(e,t,i)=>{i.d(t,{h:()=>l});var s=i(2115),r=i(4222),a=i(2302),n=i(6720);let l=()=>{let{isInitialized:e,isLoggedIn:t,userProfile:i}=(0,r.x)(),[l,o]=(0,s.useState)({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!0,error:null}),d=async()=>{if(!e||!t||!i)return void o(e=>({...e,loading:!1,isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null}));try{if(o(e=>({...e,loading:!0,error:null})),!a.b.hasValidToken()&&(console.log("No valid JWT token, performing backend login..."),!await (0,n.UK)()))throw Error("Failed to obtain JWT token");let e=await a.A.user.getProfile();if(e&&e.id){let t=!!e.id,i=!!e.isVerified,s=!!e.lineVerified,r=t&&i&&s;o({isRegistered:t,isPhoneVerified:i,isLineVerified:s,isFullyVerified:r,userInfo:e,loading:!1,error:null})}else o({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!1,error:null})}catch(e){var s;console.error("檢查會員狀態失敗:",e),(null==(s=e.response)?void 0:s.status)===404?o({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!1,error:null}):o(t=>{var i,s;return{...t,loading:!1,error:(null==(s=e.response)||null==(i=s.data)?void 0:i.message)||"檢查會員狀態失敗"}})}};return(0,s.useEffect)(()=>{e&&d()},[e,t,i]),{...l,refreshUserStatus:()=>{d()}}}},7550:(e,t,i)=>{i.d(t,{A:()=>s});let s=(0,i(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7712:(e,t,i)=>{i.d(t,{A:()=>s});let s=(0,i(9946).A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},7809:(e,t,i)=>{i.d(t,{A:()=>s});let s=(0,i(9946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},9053:(e,t,i)=>{i.d(t,{A:()=>x});var s=i(5155),r=i(2115),a=i(5695),n=i(4222),l=i(6681),o=i(7312),d=i(5339),c=i(1007),m=i(6874),u=i.n(m);function x(e){let{children:t,requireAuth:i=!0,requireVerification:m=!0}=e,x=(0,a.useRouter)(),{isInitialized:h,isLoggedIn:p}=(0,n.x)(),{isFullyVerified:g,loading:f}=(0,l.h)();return((0,r.useEffect)(()=>{h&&i&&!p&&x.push("/")},[h,p,i,x]),!h||f)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center gradient-bg",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 gradient-orange rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,s.jsx)(o.A,{className:"w-8 h-8 text-white"})}),(0,s.jsx)("p",{className:"text-lg font-medium text-gray-700",children:"載入中..."}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"正在檢查會員狀態"})]})}):i&&!p?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center gradient-bg",children:(0,s.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl shadow-warm p-8 max-w-md mx-4 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(d.A,{className:"w-8 h-8 text-red-500"})}),(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"需要登入"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"請先登入以使用此功能"}),(0,s.jsx)(u(),{href:"/",className:"w-full py-3 px-6 gradient-green text-white font-semibold rounded-2xl btn-hover shadow-warm inline-block",children:"返回首頁"})]})}):m&&p&&!g?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center gradient-bg",children:(0,s.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl shadow-warm p-8 max-w-md mx-4 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(c.A,{className:"w-8 h-8 text-blue-500"})}),(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"需要會員驗證"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"請先完成會員註冊和手機驗證以使用此功能"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(u(),{href:"/register",className:"w-full py-3 px-6 bg-blue-500 text-white font-semibold rounded-2xl btn-hover shadow-warm inline-block",children:"立即註冊"}),(0,s.jsx)(u(),{href:"/",className:"w-full py-3 px-6 bg-gray-200 text-gray-700 font-semibold rounded-2xl btn-hover inline-block",children:"返回首頁"})]})]})}):(0,s.jsx)(s.Fragment,{children:t})}}}]);