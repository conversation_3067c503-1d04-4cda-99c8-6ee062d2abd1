(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return h},defaultHead:function(){return u}});let s=r(4985),i=r(740),a=r(687),n=i._(r(3210)),l=s._(r(7755)),o=r(4959),d=r(9513),c=r(4604);function u(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function m(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===n.default.Fragment?e.concat(n.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(148);let p=["name","httpEquiv","charSet","itemProp"];function x(e,t){let{inAmpMode:r}=t;return e.reduce(m,[]).reverse().concat(u(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,s={};return i=>{let a=!0,n=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){n=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?a=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?a=!1:t.add(i.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(i.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?a=!1:r.add(t);else{let e=i.props[t],r=s[t]||new Set;("name"!==t||!n)&&r.has(e)?a=!1:(r.add(e),s[t]=r)}}}return a}}()).reverse().map((e,t)=>{let s=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,n.default.cloneElement(e,t)}return n.default.cloneElement(e,{key:s})})}let h=function(e){let{children:t}=e,r=(0,n.useContext)(o.AmpStateContext),s=(0,n.useContext)(d.HeadManagerContext);return(0,a.jsx)(l.default,{reduceComponentsToState:x,headManager:s,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1057:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\0_project\\\\augment-projects\\\\cocodrink-line\\\\client\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\page.tsx","default")},1261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return o},getImageProps:function(){return l}});let s=r(4985),i=r(4953),a=r(6533),n=s._(r(1933));function l(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let o=a.Image},1480:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:i,blurDataURL:a,objectFit:n}=e,l=s?40*s:t,o=i?40*i:r,d=l&&o?"viewBox='0 0 "+l+" "+o+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},1933:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:s,width:i,quality:a}=e,n=a||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(s)+"&w="+i+"&q="+n+(s.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},2412:e=>{"use strict";e.exports=require("assert")},2756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3649:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4604:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:s=!1}=void 0===e?{}:e;return t||r&&s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},4735:e=>{"use strict";e.exports=require("events")},4953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return o}}),r(148);let s=r(1480),i=r(2756),a=["-moz-initial","fill","none","scale-down",void 0];function n(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function o(e,t){var r,o;let d,c,u,{src:m,sizes:p,unoptimized:x=!1,priority:h=!1,loading:f,className:g,quality:b,width:j,height:y,fill:v=!1,style:w,overrideSrc:N,onLoad:_,onLoadingComplete:C,placeholder:k="empty",blurDataURL:P,fetchPriority:A,decoding:O="async",layout:S,objectFit:M,objectPosition:E,lazyBoundary:z,lazyRoot:R,...I}=e,{imgConf:q,showAltText:D,blurComplete:L,defaultLoader:T}=t,F=q||i.imageConfigDefault;if("allSizes"in F)d=F;else{let e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t),t=F.deviceSizes.sort((e,t)=>e-t),s=null==(r=F.qualities)?void 0:r.sort((e,t)=>e-t);d={...F,allSizes:e,deviceSizes:t,qualities:s}}if(void 0===T)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let G=I.loader||T;delete I.loader,delete I.srcSet;let U="__next_img_default"in G;if(U){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=G;G=t=>{let{config:r,...s}=t;return e(s)}}if(S){"fill"===S&&(v=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[S];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[S];t&&!p&&(p=t)}let B="",H=l(j),V=l(y);if((o=m)&&"object"==typeof o&&(n(o)||void 0!==o.src)){let e=n(m)?m.default:m;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,P=P||e.blurDataURL,B=e.src,!v)if(H||V){if(H&&!V){let t=H/e.width;V=Math.round(e.height*t)}else if(!H&&V){let t=V/e.height;H=Math.round(e.width*t)}}else H=e.width,V=e.height}let W=!h&&("lazy"===f||void 0===f);(!(m="string"==typeof m?m:B)||m.startsWith("data:")||m.startsWith("blob:"))&&(x=!0,W=!1),d.unoptimized&&(x=!0),U&&!d.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(x=!0);let X=l(b),Z=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:M,objectPosition:E}:{},D?{}:{color:"transparent"},w),$=L||"empty"===k?null:"blur"===k?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:H,heightInt:V,blurWidth:c,blurHeight:u,blurDataURL:P||"",objectFit:Z.objectFit})+'")':'url("'+k+'")',J=a.includes(Z.objectFit)?"fill"===Z.objectFit?"100% 100%":"cover":Z.objectFit,Y=$?{backgroundSize:J,backgroundPosition:Z.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:$}:{},K=function(e){let{config:t,src:r,unoptimized:s,width:i,quality:a,sizes:n,loader:l}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:o,kind:d}=function(e,t,r){let{deviceSizes:s,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,n),c=o.length-1;return{sizes:n||"w"!==d?n:"100vw",srcSet:o.map((e,s)=>l({config:t,src:r,quality:a,width:e})+" "+("w"===d?e:s+1)+d).join(", "),src:l({config:t,src:r,quality:a,width:o[c]})}}({config:d,src:m,unoptimized:x,width:H,quality:X,sizes:p,loader:G});return{props:{...I,loading:W?"lazy":f,fetchPriority:A,width:H,height:V,decoding:O,className:g,style:{...Z,...Y},sizes:K.sizes,srcSet:K.srcSet,src:N||K.src},meta:{unoptimized:x,priority:h,placeholder:k,fill:v}}}},4959:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.AmpContext},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6085:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},6533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return y}});let s=r(4985),i=r(740),a=r(687),n=i._(r(3210)),l=s._(r(1215)),o=s._(r(512)),d=r(4953),c=r(2756),u=r(7903);r(148);let m=r(9148),p=s._(r(1933)),x=r(3038),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function f(e,t,r,s,i,a,n){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let s=!1,i=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>s,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{s=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==s?void 0:s.current)&&s.current(e)}}))}function g(e){return n.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let b=(0,n.forwardRef)((e,t)=>{let{src:r,srcSet:s,sizes:i,height:l,width:o,decoding:d,className:c,style:u,fetchPriority:m,placeholder:p,loading:h,unoptimized:b,fill:j,onLoadRef:y,onLoadingCompleteRef:v,setBlurComplete:w,setShowAltText:N,sizesInput:_,onLoad:C,onError:k,...P}=e,A=(0,n.useCallback)(e=>{e&&(k&&(e.src=e.src),e.complete&&f(e,p,y,v,w,b,_))},[r,p,y,v,w,k,b,_]),O=(0,x.useMergedRef)(t,A);return(0,a.jsx)("img",{...P,...g(m),loading:h,width:o,height:l,decoding:d,"data-nimg":j?"fill":"1",className:c,style:u,sizes:i,srcSet:s,src:r,ref:O,onLoad:e=>{f(e.currentTarget,p,y,v,w,b,_)},onError:e=>{N(!0),"empty"!==p&&w(!0),k&&k(e)}})});function j(e){let{isAppRouter:t,imgAttributes:r}=e,s={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...g(r.fetchPriority)};return t&&l.default.preload?(l.default.preload(r.src,s),null):(0,a.jsx)(o.default,{children:(0,a.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...s},"__nimg-"+r.src+r.srcSet+r.sizes)})}let y=(0,n.forwardRef)((e,t)=>{let r=(0,n.useContext)(m.RouterContext),s=(0,n.useContext)(u.ImageConfigContext),i=(0,n.useMemo)(()=>{var e;let t=h||s||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),i=t.deviceSizes.sort((e,t)=>e-t),a=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:i,qualities:a}},[s]),{onLoad:l,onLoadingComplete:o}=e,x=(0,n.useRef)(l);(0,n.useEffect)(()=>{x.current=l},[l]);let f=(0,n.useRef)(o);(0,n.useEffect)(()=>{f.current=o},[o]);let[g,y]=(0,n.useState)(!1),[v,w]=(0,n.useState)(!1),{props:N,meta:_}=(0,d.getImgProps)(e,{defaultLoader:p.default,imgConf:i,blurComplete:g,showAltText:v});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b,{...N,unoptimized:_.unoptimized,placeholder:_.placeholder,fill:_.fill,onLoadRef:x,onLoadingCompleteRef:f,setBlurComplete:y,setShowAltText:w,sizesInput:e.sizes,ref:t}),_.priority?(0,a.jsx)(j,{isAppRouter:!r,imgAttributes:N}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6998:(e,t,r)=>{Promise.resolve().then(r.bind(r,7403))},7403:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(687),i=r(584),a=r(7979),n=r(3210),l=r(2013);let o=()=>{let[e,t]=(0,n.useState)(!1),[r,s]=(0,n.useState)(!1);return{isModalOpen:e,isLoggingOut:r,showLogoutConfirm:()=>{t(!0)},hideLogoutConfirm:()=>{t(!1)},logoutDirectly:async()=>{s(!0);try{await (0,l.Bo)()}catch(e){console.error("Direct logout failed:",e),s(!1)}},confirmLogout:async()=>{s(!0);try{await (0,l.Bo)()}catch(e){console.error("Confirmed logout failed:",e),s(!1),t(!1)}}}};var d=r(1261),c=r.n(d),u=r(5814),m=r.n(u),p=r(3166),x=r(6085),h=r(2688);let f=(0,h.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]),g=(0,h.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]),b=(0,h.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var j=r(8869),y=r(3613),v=r(1057),w=r(3649),N=r(1860);function _({isOpen:e,onClose:t,onConfirm:r}){let[i,a]=(0,n.useState)(!1),o=async()=>{a(!0);try{r&&r(),await (0,l.Bo)()}catch(e){console.error("Logout error:",e),a(!1),t()}};return e?(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-red-600 px-6 py-4 text-white",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-white/20 rounded-full flex items-center justify-center",children:(0,s.jsx)(w.A,{className:"w-4 h-4"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"確認登出"})]}),!i&&(0,s.jsx)("button",{onClick:t,className:"p-1 hover:bg-white/20 rounded-full transition-colors",children:(0,s.jsx)(N.A,{className:"w-5 h-5"})})]})}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(f,{className:"w-8 h-8 text-red-500"})}),(0,s.jsx)("p",{className:"text-gray-700 text-lg mb-2",children:"您確定要登出嗎？"}),(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"登出後您需要重新登入才能使用會員功能"})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)("button",{onClick:t,disabled:i,className:"flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"取消"}),(0,s.jsx)("button",{onClick:o,disabled:i,className:"flex-1 px-4 py-3 bg-red-500 text-white rounded-xl font-medium hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,s.jsx)("span",{children:"登出中..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"確認登出"})]})})]})]})]})}):null}function C(){let{isInitialized:e,isLoggedIn:t,userProfile:r,error:d,isDevelopmentMode:u}=(0,i.x)(),{isFullyVerified:h,loading:w}=(0,a.h)(),{isModalOpen:N,isLoggingOut:C,showLogoutConfirm:k,hideLogoutConfirm:P,confirmLogout:A}=o(),[O,S]=(0,n.useState)(!0);return(u&&console.log("開發模式已啟用，繞過 LIFF 登入"),O)?(0,s.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(p.A,{className:"w-16 h-16 text-orange-500 mx-auto mb-4 animate-pulse"}),(0,s.jsx)("div",{className:"absolute -top-2 -right-2",children:(0,s.jsx)(x.A,{className:"w-6 h-6 text-yellow-500 animate-bounce"})})]}),(0,s.jsx)("p",{className:"text-xl font-semibold text-orange-800 mb-2",children:"COCO 飲料店"}),(0,s.jsx)("p",{className:"text-orange-600",children:"正在為您準備最棒的飲品體驗..."})]})}):d?(0,s.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-3xl shadow-warm p-8 max-w-md w-full text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(p.A,{className:"w-8 h-8 text-red-500"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"哎呀！出了點問題"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:d.message}),(0,s.jsx)("button",{className:"w-full py-3 px-6 gradient-orange text-white font-semibold rounded-2xl btn-hover shadow-warm",onClick:()=>window.location.reload(),children:"重新載入"})]})}):(0,s.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,s.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 gradient-orange rounded-full flex items-center justify-center",children:(0,s.jsx)(p.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"COCO"}),(0,s.jsx)("p",{className:"text-xs text-gray-600",children:"飲料專門店"})]})]}),(0,s.jsx)("div",{children:t?(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[r.pictureUrl&&(0,s.jsx)(c(),{src:r.pictureUrl,alt:"Profile",width:36,height:36,className:"rounded-full border-2 border-orange-200"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700 hidden sm:block",children:r.displayName})]}),(0,s.jsx)("button",{className:"p-2 bg-red-50 text-red-600 rounded-full hover:bg-red-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",onClick:k,disabled:C,title:C?"登出中...":"登出",children:C?(0,s.jsx)("div",{className:"w-5 h-5 border-2 border-red-600/30 border-t-red-600 rounded-full animate-spin"}):(0,s.jsx)(f,{className:"w-5 h-5"})})]}):(0,s.jsxs)("button",{className:"flex items-center gap-2 px-4 py-2 gradient-green text-white rounded-full btn-hover shadow-warm",onClick:l.Rf,children:[(0,s.jsx)(g,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"LINE 登入"})]})})]})}),(0,s.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,s.jsx)("div",{className:"max-w-md mx-auto",children:(0,s.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl shadow-warm p-8 mb-6 card-float",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"relative inline-block mb-4",children:[(0,s.jsx)("div",{className:"w-20 h-20 gradient-orange rounded-full flex items-center justify-center mx-auto",children:(0,s.jsx)(p.A,{className:"w-10 h-10 text-white"})}),(0,s.jsx)("div",{className:"absolute -top-1 -right-1",children:(0,s.jsx)(b,{className:"w-6 h-6 text-red-500 animate-pulse"})})]}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"歡迎來到 COCO"}),(0,s.jsx)("p",{className:"text-gray-600 text-lg",children:"最棒的手搖飲品體驗"})]}),t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"bg-orange-50 rounded-2xl p-4 mb-6 text-center",children:[(0,s.jsxs)("p",{className:"text-orange-800 font-medium",children:["嗨！",r?.displayName||"親愛的顧客"," \uD83D\uDC4B"]}),(0,s.jsx)("p",{className:"text-orange-600 text-sm mt-1",children:"準備好享受美味飲品了嗎？"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[!h&&(0,s.jsxs)(m(),{href:"/register",className:"flex items-center justify-between w-full py-4 px-6 bg-blue-500 text-white rounded-2xl btn-hover shadow-warm group",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(j.A,{className:"w-6 h-6"}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("p",{className:"font-semibold",children:"會員註冊/驗證"}),(0,s.jsx)("p",{className:"text-blue-100 text-sm",children:"享受會員專屬優惠"})]})]}),(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center group-hover:bg-blue-300 transition-colors",children:(0,s.jsx)("span",{className:"text-white text-sm",children:"→"})})]}),h?(0,s.jsxs)(m(),{href:"/products",className:"flex items-center justify-between w-full py-4 px-6 gradient-green text-white rounded-2xl btn-hover shadow-warm group",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(p.A,{className:"w-6 h-6"}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("p",{className:"font-semibold",children:"開始點餐"}),(0,s.jsx)("p",{className:"text-green-100 text-sm",children:"探索美味飲品"})]})]}),(0,s.jsx)("div",{className:"w-8 h-8 bg-green-400 rounded-full flex items-center justify-center group-hover:bg-green-300 transition-colors",children:(0,s.jsx)("span",{className:"text-white text-sm",children:"→"})})]}):(0,s.jsxs)("div",{className:"flex items-center justify-between w-full py-4 px-6 bg-gray-300 text-gray-500 rounded-2xl cursor-not-allowed",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(p.A,{className:"w-6 h-6"}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("p",{className:"font-semibold",children:"開始點餐"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"請先完成會員註冊"})]})]}),(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center",children:(0,s.jsx)(y.A,{className:"w-4 h-4 text-gray-500"})})]}),h?(0,s.jsxs)(m(),{href:"/orders",className:"flex items-center justify-between w-full py-4 px-6 gradient-purple text-white rounded-2xl btn-hover shadow-warm group",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(v.A,{className:"w-6 h-6"}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("p",{className:"font-semibold",children:"我的訂單"}),(0,s.jsx)("p",{className:"text-purple-100 text-sm",children:"查看訂單狀態"})]})]}),(0,s.jsx)("div",{className:"w-8 h-8 bg-purple-400 rounded-full flex items-center justify-center group-hover:bg-purple-300 transition-colors",children:(0,s.jsx)("span",{className:"text-white text-sm",children:"→"})})]}):(0,s.jsxs)("div",{className:"flex items-center justify-between w-full py-4 px-6 bg-gray-300 text-gray-500 rounded-2xl cursor-not-allowed",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(v.A,{className:"w-6 h-6"}),(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("p",{className:"font-semibold",children:"我的訂單"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"請先完成會員註冊"})]})]}),(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center",children:(0,s.jsx)(y.A,{className:"w-4 h-4 text-gray-500"})})]})]})]}):(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"bg-yellow-50 rounded-2xl p-6 mb-6",children:[(0,s.jsx)(x.A,{className:"w-12 h-12 text-yellow-500 mx-auto mb-3"}),(0,s.jsx)("p",{className:"text-gray-700 font-medium mb-2",children:"開始您的美味之旅"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"請先登入以使用線上點餐系統"})]}),(0,s.jsxs)("button",{className:"w-full py-4 px-6 gradient-green text-white font-semibold rounded-2xl btn-hover shadow-warm flex items-center justify-center gap-3",onClick:l.Rf,children:[(0,s.jsx)(g,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"使用 LINE 登入"})]})]})]})})}),(0,s.jsx)("footer",{className:"bg-white/60 backdrop-blur-sm border-t border-orange-100 py-6",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(0,s.jsx)(p.A,{className:"w-5 h-5 text-orange-500"}),(0,s.jsx)("span",{className:"font-semibold text-gray-700",children:"COCO 飲料店"})]}),(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"\xa9 2024 COCO飲料店. All rights reserved."}),(0,s.jsx)("p",{className:"text-gray-400 text-xs mt-1",children:"用心調製每一杯飲品 ❤️"})]})}),(0,s.jsx)(_,{isOpen:N,onClose:P,onConfirm:A})]})}},7755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let s=r(3210),i=()=>{},a=()=>{};function n(e){var t;let{headManager:r,reduceComponentsToState:n}=e;function l(){if(r&&r.mountedInstances){let t=s.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(n(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),l(),i(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),i(()=>(r&&(r._pendingUpdate=l),()=>{r&&(r._pendingUpdate=l)})),a(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},7903:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.ImageConfigContext},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},8542:(e,t,r)=>{Promise.resolve().then(r.bind(r,1204))},8700:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(5239),i=r(8088),a=r(8170),n=r.n(a),l=r(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9148:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.RouterContext},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9513:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.HeadManagerContext},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,41,248,947],()=>r(8700));module.exports=s})();