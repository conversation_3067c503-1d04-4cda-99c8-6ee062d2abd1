module.exports = (sequelize, Sequelize) => {
  const SystemSetting = sequelize.define('systemSetting', {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    key: {
      type: Sequelize.STRING,
      unique: true,
      allowNull: false,
      comment: '設定鍵名'
    },
    value: {
      type: Sequelize.TEXT,
      allowNull: false,
      comment: '設定值'
    },
    description: {
      type: Sequelize.TEXT,
      comment: '設定描述'
    },
    type: {
      type: Sequelize.ENUM('string', 'number', 'boolean', 'json'),
      defaultValue: 'string',
      comment: '值的類型'
    }
  }, {
    timestamps: true,
    tableName: 'system_settings'
  });

  return SystemSetting;
};
