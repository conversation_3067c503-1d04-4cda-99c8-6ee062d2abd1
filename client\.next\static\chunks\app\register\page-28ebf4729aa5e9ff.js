(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},977:(e,s,t)=>{Promise.resolve().then(t.bind(t,6014))},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},6014:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var a=t(5155),r=t(4222),l=t(2302),n=t(6720),i=t(6874),d=t.n(i),c=t(5695),o=t(2115),x=t(7550),h=t(7312),m=t(1007),u=t(9946);let g=(0,u.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),f=(0,u.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var p=t(646);function b(){let e=(0,c.useRouter)(),{isInitialized:s,isLoggedIn:t,userProfile:i}=(0,r.x)(),[u,b]=(0,o.useState)(!0),[j,y]=(0,o.useState)(null),[N,v]=(0,o.useState)(null),[w,k]=(0,o.useState)(""),[A,C]=(0,o.useState)(""),[F,S]=(0,o.useState)(""),[O,_]=(0,o.useState)(1),[M,P]=(0,o.useState)(!1),[z,E]=(0,o.useState)(!1),[R,q]=(0,o.useState)(0);(0,o.useEffect)(()=>{(async()=>{if(s&&t)try{let e=await (0,n.VM)();k(e.displayName||""),b(!1)}catch(e){console.error("Failed to get user profile",e),y("無法獲取用戶資料，請重新登入"),b(!1)}else s&&!t&&e.push("/")})()},[s,t,e]);let I=async()=>{if(!A)return void y("請輸入手機號碼");if(!/^09\d{8}$/.test(A))return void y("請輸入有效的台灣手機號碼（例如：0912345678）");P(!0),y(null);try{1===O&&await l.A.auth.register((null==i?void 0:i.userId)||"",w,A),await l.A.auth.sendVerification(A),_(2),v("驗證碼已發送到您的手機"),q(60);let e=setInterval(()=>{q(s=>s<=1?(clearInterval(e),0):s-1)},1e3)}catch(t){var e,s;console.error("Failed to send verification code",t),y((null==(s=t.response)||null==(e=s.data)?void 0:e.message)||"發送驗證碼失敗，請稍後再試")}finally{P(!1)}},V=async()=>{if(!F)return void y("請輸入驗證碼");E(!0),y(null);try{await l.A.auth.verifyPhone(A,F),v("手機驗證成功"),setTimeout(()=>{e.push("/")},2e3)}catch(e){var s,t;console.error("Failed to verify phone",e),y((null==(t=e.response)||null==(s=t.data)?void 0:s.message)||"驗證失敗，請檢查驗證碼是否正確")}finally{E(!1)}};return u?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-lg",children:"載入中..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,a.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(d(),{href:"/",className:"p-2 bg-orange-100 text-orange-600 rounded-full hover:bg-orange-200 transition-colors",children:(0,a.jsx)(x.A,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 gradient-orange rounded-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"w-5 h-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-lg font-bold text-gray-800",children:"COCO"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:"會員註冊"})]})]})]})})}),(0,a.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"max-w-md mx-auto",children:(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl shadow-warm p-8 card-float",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)("div",{className:"relative inline-block mb-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 gradient-orange rounded-full flex items-center justify-center mx-auto",children:1===O?(0,a.jsx)(m.A,{className:"w-8 h-8 text-white"}):(0,a.jsx)(g,{className:"w-8 h-8 text-white"})}),2===O&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1",children:(0,a.jsx)(f,{className:"w-5 h-5 text-green-500 animate-pulse"})})]}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-2",children:1===O?"會員註冊":"手機驗證"}),(0,a.jsx)("p",{className:"text-gray-600",children:1===O?"完成註冊，享受會員專屬優惠":"請輸入收到的驗證碼"})]}),j&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-2xl mb-6 flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0",children:(0,a.jsx)("span",{className:"text-white text-xs",children:"!"})}),(0,a.jsx)("span",{className:"text-sm",children:j})]}),N&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-2xl mb-6 flex items-center gap-3",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm",children:N})]}),1===O?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"name",className:"flex items-center gap-2 text-gray-700 font-medium mb-3",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 text-orange-500"}),"姓名"]}),(0,a.jsx)("input",{type:"text",id:"name",value:w,onChange:e=>k(e.target.value),className:"w-full px-4 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all bg-gray-50 hover:bg-white",placeholder:"請輸入您的姓名",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"phone",className:"flex items-center gap-2 text-gray-700 font-medium mb-3",children:[(0,a.jsx)(f,{className:"w-4 h-4 text-orange-500"}),"手機號碼"]}),(0,a.jsx)("input",{type:"tel",id:"phone",value:A,onChange:e=>C(e.target.value),placeholder:"例如：0912345678",className:"w-full px-4 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all bg-gray-50 hover:bg-white",required:!0})]}),(0,a.jsx)("button",{onClick:I,disabled:M||!w||!A,className:"w-full py-4 px-6 gradient-orange text-white rounded-2xl font-semibold btn-hover shadow-warm disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2",children:M?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),"處理中..."]}):(0,a.jsxs)(a.Fragment,{children:["下一步",(0,a.jsx)(x.A,{className:"w-5 h-5 rotate-180"})]})})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"code",className:"flex items-center gap-2 text-gray-700 font-medium mb-3",children:[(0,a.jsx)(g,{className:"w-4 h-4 text-green-500"}),"驗證碼"]}),(0,a.jsx)("input",{type:"text",id:"code",value:F,onChange:e=>S(e.target.value),className:"w-full px-4 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all bg-gray-50 hover:bg-white text-center text-2xl tracking-widest",placeholder:"請輸入 6 位數驗證碼",maxLength:6,required:!0}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-2 text-center",children:["已發送驗證碼到 ",A]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("button",{onClick:V,disabled:z||!F,className:"w-full py-4 px-6 gradient-green text-white rounded-2xl font-semibold btn-hover shadow-warm disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2",children:z?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),"驗證中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"w-5 h-5"}),"完成驗證"]})}),(0,a.jsx)("button",{onClick:I,disabled:M||R>0,className:"w-full py-3 px-4 bg-gray-100 text-gray-600 rounded-2xl font-medium hover:bg-gray-200 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:R>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-xs",children:R})}),"重新發送驗證碼"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f,{className:"w-4 h-4"}),"重新發送驗證碼"]})})]})]})]})})}),(0,a.jsxs)("footer",{className:"bg-white/50 backdrop-blur-sm p-6 text-center",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-2",children:[(0,a.jsx)("div",{className:"w-6 h-6 gradient-orange rounded-full flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"w-4 h-4 text-white"})}),(0,a.jsx)("span",{className:"text-gray-700 font-medium",children:"COCO 飲料專門店"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"\xa9 2024 COCO. All rights reserved."})]})]})}},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[932,659,222,441,684,358],()=>s(977)),_N_E=e.O()}]);