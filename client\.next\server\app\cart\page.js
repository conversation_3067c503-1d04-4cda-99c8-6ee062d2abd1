(()=>{var e={};e.id=5,e.ids=[5],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},905:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\0_project\\\\augment-projects\\\\cocodrink-line\\\\client\\\\src\\\\app\\\\cart\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\cart\\page.tsx","default")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},2412:e=>{"use strict";e.exports=require("assert")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4545:(e,t,s)=>{Promise.resolve().then(s.bind(s,9626))},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6234:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),l=s(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["cart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,905)),"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\cart\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\cart\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/cart/page",pathname:"/cart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7190:(e,t,s)=>{Promise.resolve().then(s.bind(s,905))},7796:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(687);function a({item:e,onEditItem:t,canEdit:s=!1}){return(0,r.jsx)("div",{className:"border-l-4 border-purple-200 pl-4 py-3 bg-gray-50 rounded-r-lg",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[s&&t?(0,r.jsxs)("button",{onClick:()=>t(e),className:"font-medium text-purple-600 hover:text-purple-800 hover:underline transition-colors",children:[e.product?.name||"商品"," \xd7 ",e.quantity]}):(0,r.jsxs)("span",{className:"font-medium text-gray-800",children:[e.product?.name||"商品"," \xd7 ",e.quantity]}),s&&(0,r.jsx)("span",{className:"text-xs text-gray-400",children:"(點擊修改)"})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["原價: NT$ ",e.product?.price||0]}),(()=>{if(!e.customizations)return null;let t=[];return e.customizations.sugar&&t.push((0,r.jsxs)("span",{className:"text-xs text-orange-600",children:["\uD83C\uDF6F ",e.customizations.sugar]},"sugar")),e.customizations.ice&&t.push((0,r.jsxs)("span",{className:"text-xs text-blue-600",children:["\uD83E\uDDCA ",e.customizations.ice]},"ice")),t.length>0?(0,r.jsx)("div",{className:"flex items-center gap-3 mt-1",children:t}):null})(),(()=>{if(!e.customizations?.toppings||0===e.customizations.toppings.length)return null;let t=e=>({珍珠:10,椰果:10,鮮奶油:15,巧克力片:10,額外奶泡:10,布丁:12,仙草:8})[e]||10;return(0,r.jsx)("div",{className:"mt-2 space-y-1",children:e.customizations.toppings.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,r.jsxs)("span",{className:"text-green-600 flex items-center gap-1",children:[(0,r.jsx)("span",{children:"\uD83E\uDD64"}),(0,r.jsx)("span",{children:e})]}),(0,r.jsxs)("span",{className:"text-orange-500 font-medium",children:["+NT$ ",t(e)]})]},s))})})()]}),(0,r.jsxs)("div",{className:"text-right ml-4",children:[(0,r.jsxs)("div",{className:"font-semibold text-gray-800",children:["NT$ ",e.price*e.quantity]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["單價: NT$ ",e.price]})]})]})})}},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9626:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var r=s(687),a=s(769),i=s(584),n=s(5814),l=s.n(n),o=s(8559),c=s(8561),d=s(5748),x=s(6474);let u=(0,s(2688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var m=s(3210),p=s(3515),h=s(7327),g=s(6189),y=s(7796),b=s(118),f=s(2013);async function j(e){try{let t=function(e){var t,s;let r,a=e=>new Date(e).toLocaleString("zh-TW",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"});return{type:"flex",altText:`訂單確認 - ${e.orderNumber}`,contents:{type:"bubble",header:{type:"box",layout:"vertical",contents:[{type:"text",text:"\uD83C\uDF89 訂單確認",weight:"bold",size:"xl",color:"#ffffff"},{type:"text",text:"感謝您的訂購！",size:"sm",color:"#ffffff",margin:"sm"}],backgroundColor:"#FF6B35",paddingAll:"20px"},body:{type:"box",layout:"vertical",contents:[{type:"box",layout:"vertical",contents:[{type:"text",text:"訂單資訊",weight:"bold",size:"md",color:"#333333"},{type:"box",layout:"vertical",contents:[{type:"box",layout:"horizontal",contents:[{type:"text",text:"訂單編號",size:"sm",color:"#666666",flex:2},{type:"text",text:e.orderNumber,size:"sm",weight:"bold",flex:3,align:"end"}]},{type:"box",layout:"horizontal",contents:[{type:"text",text:"訂單時間",size:"sm",color:"#666666",flex:2},{type:"text",text:a(e.createdAt),size:"sm",flex:3,align:"end"}],margin:"sm"},{type:"box",layout:"horizontal",contents:[{type:"text",text:"訂單類型",size:"sm",color:"#666666",flex:2},{type:"text",text:(t=e.orderType,s=e.scheduledTime,"scheduled"===t&&s?`預約訂購 - ${a(s)}`:"立即訂購"),size:"sm",flex:3,align:"end"}],margin:"sm"}],margin:"md"}]},{type:"separator",margin:"xl"},{type:"box",layout:"vertical",contents:[{type:"text",text:"訂購商品",weight:"bold",size:"md",color:"#333333"},{type:"box",layout:"vertical",contents:(r=e.items).map((e,t)=>{let s=e.customizations?Object.entries(e.customizations).filter(([e,t])=>null!=t).map(([e,t])=>"addOns"===e&&Array.isArray(t)?t.map(e=>`+${e.name}`).join(", "):`${t}`).join(" | "):"";return{type:"box",layout:"vertical",contents:[{type:"box",layout:"horizontal",contents:[{type:"text",text:`${e.product.name}`,weight:"bold",size:"sm",flex:3},{type:"text",text:`x${e.quantity}`,size:"sm",color:"#666666",flex:1,align:"end"},{type:"text",text:`NT$${e.price}`,size:"sm",color:"#666666",flex:1,align:"end"}]},...s?[{type:"text",text:s,size:"xs",color:"#999999",wrap:!0,margin:"xs"}]:[],...t<r.length-1?[{type:"separator",margin:"md"}]:[]],margin:t>0?"md":"none"}}),margin:"md"}],margin:"xl"},{type:"separator",margin:"xl"},{type:"box",layout:"horizontal",contents:[{type:"text",text:"總計",weight:"bold",size:"lg",color:"#333333"},{type:"text",text:`NT$${e.totalPrice}`,weight:"bold",size:"lg",color:"#FF6B35",align:"end"}],margin:"xl"},...e.note?[{type:"separator",margin:"xl"},{type:"box",layout:"vertical",contents:[{type:"text",text:"備註",weight:"bold",size:"sm",color:"#666666"},{type:"text",text:e.note,size:"sm",wrap:!0,margin:"sm"}],margin:"xl"}]:[]],paddingAll:"20px"},footer:{type:"box",layout:"vertical",contents:[{type:"button",action:{type:"uri",label:"查看訂單詳情",uri:"https://liff.line.me/2007460761-vMp0L0WA/orders"},style:"primary",color:"#FF6B35"}],paddingAll:"20px"}}}}(e);return await (0,f.bU)([t]),console.log("訂單確認訊息已發送"),!0}catch(e){return console.error("發送訂單確認訊息失敗:",e),!1}}function v(){let{userProfile:e}=(0,i.x)(),t=(0,g.useRouter)(),{cartItems:s,addToCart:n,updateQuantity:f,removeFromCart:v,clearCart:N,getTotalPrice:w,getTotalQuantity:z,isEmpty:k,formatOrderData:q}=(0,p._)(),[A,C]=(0,m.useState)(!1),[_,P]=(0,m.useState)("immediate"),[D,$]=(0,m.useState)(""),[T,E]=(0,m.useState)(""),[I,F]=(0,m.useState)(null),[M,S]=(0,m.useState)(!1),G=async()=>{if(!k){C(!0);try{let e=q(_,"scheduled"===_?new Date(D):void 0,T||void 0),s=await h.A.orders.create(e),r=s.data||s;try{await j(r)?console.log("訂單確認訊息已發送到 LINE"):console.log("無法發送訂單確認訊息（可能不在 LINE 環境中）")}catch(e){console.error("發送訂單確認訊息時發生錯誤:",e)}N(),alert(`訂單建立成功！訂單編號：${r.orderNumber}`),t.push("/orders")}catch(e){console.error("Failed to create order:",e),alert(e.response?.data?.message||"訂單建立失敗，請稍後再試")}finally{C(!1)}}},O=async e=>{try{let t=(await h.A.products.getAll()).find(t=>t.id===e.productId);if(!t)return void console.error("找不到產品資訊");F({...t,cartItem:e,currentCustomizations:e.customizations}),S(!0)}catch(e){console.error("獲取產品資訊失敗:",e)}};return(0,r.jsx)(a.A,{requireAuth:!0,requireVerification:!0,children:(0,r.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,r.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(l(),{href:"/products",className:"p-2 bg-green-100 text-green-600 rounded-full hover:bg-green-200 transition-colors",children:(0,r.jsx)(o.A,{className:"w-5 h-5"})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(c.A,{className:"w-6 h-6 text-green-500"}),(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"購物車"})]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[z()," 件商品"]})]})}),(0,r.jsx)("main",{className:"container mx-auto px-4 py-6",children:k?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(c.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-600 mb-2",children:"購物車是空的"}),(0,r.jsx)("p",{className:"text-gray-500 mb-6",children:"快去選購您喜愛的飲品吧！"}),(0,r.jsx)(l(),{href:"/products",className:"inline-block px-6 py-3 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors",children:"開始點餐"})]}):(0,r.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:"商品清單"}),(0,r.jsx)("div",{className:"space-y-3",children:s.map(e=>{let t={id:e.id,productId:e.productId,quantity:e.quantity,price:e.totalPrice,customizations:e.customizations,product:{id:e.productId,name:e.name,price:e.basePrice,image:e.image}};return(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(y.A,{item:t,onEditItem:O,canEdit:!0}),(0,r.jsxs)("div",{className:"flex items-center justify-between bg-white rounded-lg p-3 border border-gray-200",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"數量控制"}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 bg-gray-50 rounded-full px-3 py-1",children:[(0,r.jsx)("button",{onClick:()=>f(e.id,e.quantity-1),className:"p-1 hover:bg-gray-200 rounded-full transition-colors",disabled:e.quantity<=1,children:(0,r.jsx)(d.A,{className:"w-4 h-4"})}),(0,r.jsx)("span",{className:"w-8 text-center text-sm font-medium",children:e.quantity}),(0,r.jsx)("button",{onClick:()=>f(e.id,e.quantity+1),className:"p-1 hover:bg-gray-200 rounded-full transition-colors",children:(0,r.jsx)(x.A,{className:"w-4 h-4"})})]}),(0,r.jsxs)("button",{onClick:()=>v(e.id),className:"flex items-center gap-1 px-3 py-1 bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors text-sm",title:"移除商品",children:[(0,r.jsx)(u,{className:"w-4 h-4"}),"移除"]})]})]})]},e.id)})})]}),(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:"訂單選項"}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"訂單類型"}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"radio",value:"immediate",checked:"immediate"===_,onChange:e=>P(e.target.value),className:"mr-2"}),"立即訂購"]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"radio",value:"scheduled",checked:"scheduled"===_,onChange:e=>P(e.target.value),className:"mr-2"}),"預約訂購"]})]})]}),"scheduled"===_&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"預約時間"}),(0,r.jsx)("input",{type:"datetime-local",value:D,onChange:e=>$(e.target.value),min:(()=>{let e=new Date;return e.setMinutes(e.getMinutes()+30),e.toISOString().slice(0,16)})(),className:"w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",required:!0})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"備註"}),(0,r.jsx)("textarea",{value:T,onChange:e=>E(e.target.value),placeholder:"有什麼特殊需求嗎？",className:"w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",rows:3})]})]}),(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("span",{className:"text-xl font-bold text-gray-800",children:"總計"}),(0,r.jsxs)("span",{className:"text-2xl font-bold text-green-500",children:["NT$ ",w()]})]}),(0,r.jsx)("button",{onClick:G,disabled:A||"scheduled"===_&&!D,className:"w-full py-4 px-6 bg-green-500 text-white rounded-2xl font-semibold hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:A?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),"處理中..."]}):"確認訂單"})]})]})}),I&&(0,r.jsx)(b.A,{product:I,isOpen:M,onClose:()=>{S(!1),F(null)},onAddToCart:(e,t,s)=>{v(I.cartItem.id),n(e,t,s),S(!1),F(null)},isEditMode:!0,currentCustomizations:I.currentCustomizations})]})})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,41,248,947,507],()=>s(6234));module.exports=r})();