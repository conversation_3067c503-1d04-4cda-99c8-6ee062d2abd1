const db = require('../models');
const Order = db.orders;
const OrderItem = db.orderItems;
const Product = db.products;
const User = db.users;

// 生成訂單編號
const generateOrderNumber = async () => {
  const today = new Date();
  const dateStr = today.toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD

  // 查找今天已有的訂單數量
  const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const todayEnd = new Date(todayStart);
  todayEnd.setDate(todayEnd.getDate() + 1);

  const { Op } = require('sequelize');
  const todayOrderCount = await Order.count({
    where: {
      createdAt: {
        [Op.gte]: todayStart,
        [Op.lt]: todayEnd
      }
    }
  });

  const orderSequence = String(todayOrderCount + 1).padStart(3, '0');
  return `ORD-${dateStr}-${orderSequence}`;
};

// 創建新訂單
exports.createOrder = async (req, res) => {
  try {
    const userId = req.userId;
    const { items, totalPrice, orderType, scheduledTime, note } = req.body;

    // 檢查用戶是否存在
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ message: '用戶不存在' });
    }

    // 檢查用戶是否已驗證手機
    if (!user.isVerified) {
      return res.status(403).json({ message: '請先驗證您的手機號碼' });
    }

    // 生成訂單編號
    const orderNumber = await generateOrderNumber();

    // 創建訂單
    const order = await Order.create({
      orderNumber,
      userId,
      totalPrice,
      status: 'pending',
      orderType,
      scheduledTime: orderType === 'scheduled' ? scheduledTime : null,
      note
    });

    // 創建訂單項目
    const orderItems = [];
    for (const item of items) {
      const product = await Product.findByPk(item.productId);
      if (!product) {
        return res.status(404).json({ message: `產品 ID ${item.productId} 不存在` });
      }

      const orderItem = await OrderItem.create({
        orderId: order.id,
        productId: item.productId,
        quantity: item.quantity,
        price: item.price,
        customizations: item.customizations
      });

      orderItems.push(orderItem);
    }

    // 通知店家有新訂單
    // 發送 WebSocket 通知給管理員
    const socketHandler = req.app.get('socketHandler');
    if (socketHandler) {
      await socketHandler.notifyNewOrder(order);
    }

    res.status(201).json({
      message: '訂單創建成功',
      order: {
        id: order.id,
        orderNumber: order.orderNumber,
        userId: order.userId,
        totalPrice: order.totalPrice,
        status: order.status,
        orderType: order.orderType,
        scheduledTime: order.scheduledTime,
        note: order.note,
        createdAt: order.createdAt,
        items: orderItems
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '伺服器錯誤' });
  }
};

// 獲取訂單詳情
exports.getOrderById = async (req, res) => {
  try {
    const userId = req.userId;
    const { id } = req.params;

    const order = await Order.findOne({
      where: { id, userId },
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'price', 'image']
            }
          ]
        }
      ]
    });

    if (!order) {
      return res.status(404).json({ message: '訂單不存在' });
    }

    res.status(200).json(order);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '伺服器錯誤' });
  }
};

// 取消訂單
exports.cancelOrder = async (req, res) => {
  try {
    const userId = req.userId;
    const { id } = req.params;

    const order = await Order.findOne({
      where: { id, userId }
    });

    if (!order) {
      return res.status(404).json({ message: '訂單不存在' });
    }

    // 只有待處理的訂單可以取消
    if (order.status !== 'pending') {
      return res.status(400).json({ message: '只有待處理的訂單可以取消' });
    }

    await order.update({ status: 'cancelled' });

    res.status(200).json({
      message: '訂單已取消',
      order
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '伺服器錯誤' });
  }
};

// 重新送單
exports.resubmitOrder = async (req, res) => {
  try {
    const userId = req.userId;
    const { id } = req.params;

    const order = await Order.findOne({
      where: { id, userId }
    });

    if (!order) {
      return res.status(404).json({ message: '訂單不存在' });
    }

    // 只有 pending 狀態的訂單可以重新送單
    if (order.status !== 'pending') {
      return res.status(400).json({ message: '只有等待中的訂單可以重新送單' });
    }

    // 重新發送 WebSocket 通知給管理員
    const socketHandler = req.app.get('socketHandler');
    if (socketHandler) {
      await socketHandler.notifyNewOrder(order);
    }

    res.status(200).json({
      message: '訂單已重新送單',
      order
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '重新送單失敗' });
  }
};