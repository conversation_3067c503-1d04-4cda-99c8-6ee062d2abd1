(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[778],{646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1243:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2277:(e,s,t)=>{Promise.resolve().then(t.bind(t,5187))},2322:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var a=t(5155);function l(e){var s,t,l;let{item:r,onEditItem:i,canEdit:n=!1}=e;return(0,a.jsx)("div",{className:"border-l-4 border-purple-200 pl-4 py-3 bg-gray-50 rounded-r-lg",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[n&&i?(0,a.jsxs)("button",{onClick:()=>i(r),className:"font-medium text-purple-600 hover:text-purple-800 hover:underline transition-colors",children:[(null==(s=r.product)?void 0:s.name)||"商品"," \xd7 ",r.quantity]}):(0,a.jsxs)("span",{className:"font-medium text-gray-800",children:[(null==(t=r.product)?void 0:t.name)||"商品"," \xd7 ",r.quantity]}),n&&(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"(點擊修改)"})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["原價: NT$ ",(null==(l=r.product)?void 0:l.price)||0]}),(()=>{if(!r.customizations)return null;let e=[];return r.customizations.sugar&&e.push((0,a.jsxs)("span",{className:"text-xs text-orange-600",children:["\uD83C\uDF6F ",r.customizations.sugar]},"sugar")),r.customizations.ice&&e.push((0,a.jsxs)("span",{className:"text-xs text-blue-600",children:["\uD83E\uDDCA ",r.customizations.ice]},"ice")),e.length>0?(0,a.jsx)("div",{className:"flex items-center gap-3 mt-1",children:e}):null})(),(()=>{var e;if(!(null==(e=r.customizations)?void 0:e.toppings)||0===r.customizations.toppings.length)return null;let s=e=>({珍珠:10,椰果:10,鮮奶油:15,巧克力片:10,額外奶泡:10,布丁:12,仙草:8})[e]||10;return(0,a.jsx)("div",{className:"mt-2 space-y-1",children:r.customizations.toppings.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsxs)("span",{className:"text-green-600 flex items-center gap-1",children:[(0,a.jsx)("span",{children:"\uD83E\uDD64"}),(0,a.jsx)("span",{children:e})]}),(0,a.jsxs)("span",{className:"text-orange-500 font-medium",children:["+NT$ ",s(e)]})]},t))})})()]}),(0,a.jsxs)("div",{className:"text-right ml-4",children:[(0,a.jsxs)("div",{className:"font-semibold text-gray-800",children:["NT$ ",r.price*r.quantity]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["單價: NT$ ",r.price]})]})]})})}},3841:(e,s,t)=>{"use strict";t.d(s,{_:()=>l});var a=t(2115);let l=()=>{let[e,s]=(0,a.useState)([]),[t,l]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{s(JSON.parse(e))}catch(e){console.error("Failed to load cart from localStorage:",e)}},[]),(0,a.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(e))},[e]);let r=e=>{s(s=>s.filter(s=>s.id!==e))},i=()=>e.reduce((e,s)=>e+s.totalPrice*s.quantity,0),n=0===e.length;return{cartItems:e,isLoading:t,addToCart:(e,t,a)=>{let l=a||e.price,r={id:Date.now(),productId:e.id,name:e.name,basePrice:e.price,totalPrice:l,quantity:1,image:e.image,customizations:t};s(s=>{let a=s.findIndex(s=>s.productId===e.id&&JSON.stringify(s.customizations)===JSON.stringify(t));if(!(a>-1))return[...s,r];{let e=[...s];return e[a].quantity+=1,e}})},updateQuantity:(e,t)=>{if(t<=0)return void r(e);s(s=>s.map(s=>s.id===e?{...s,quantity:t}:s))},removeFromCart:r,clearCart:()=>{s([])},getTotalPrice:i,getTotalQuantity:()=>e.reduce((e,s)=>e+s.quantity,0),isEmpty:n,formatOrderData:(s,t,a)=>({items:e.map(e=>({productId:e.productId,quantity:e.quantity,price:e.totalPrice,customizations:e.customizations||{}})),totalPrice:i(),orderType:s,scheduledTime:t,note:a})}}},4416:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5187:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var a=t(5155),l=t(9053),r=t(4222),i=t(6874),n=t.n(i),d=t(9946);let c=(0,d.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var o=t(7312),u=t(646);let x=(0,d.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var m=t(6151),h=t(7550);let p=(0,d.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var g=t(1243),f=t(2115),b=t(2302),j=t(2322),y=t(3841),N=t(4416);function v(e){let{isOpen:s,onClose:t,result:l}=e;if(!s||!l)return null;let r=l.unavailableItems.length>0||l.unavailableToppings.length>0;return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-warm max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"sticky top-0 bg-white rounded-t-3xl border-b border-gray-100 p-6 flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"再點一次結果"}),(0,a.jsx)("button",{onClick:t,className:"p-2 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors",children:(0,a.jsx)(N.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"w-6 h-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:"成功加入購物車"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["已成功重新加入 ",l.successCount," 項商品"]})]})]}),r&&(0,a.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-xl p-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,a.jsx)(g.A,{className:"w-5 h-5 text-orange-500"}),(0,a.jsx)("h4",{className:"font-semibold text-orange-800",children:"注意事項"})]}),l.unavailableItems.length>0&&(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-orange-800 mb-2",children:"以下商品已下架或不可用："}),(0,a.jsx)("ul",{className:"text-sm text-orange-700 space-y-1",children:l.unavailableItems.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"w-1.5 h-1.5 bg-orange-500 rounded-full"}),e]},s))})]}),l.unavailableToppings.length>0&&(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-orange-800 mb-2",children:"以下配料已不可用："}),(0,a.jsx)("ul",{className:"text-sm text-orange-700 space-y-1",children:l.unavailableToppings.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"w-1.5 h-1.5 bg-orange-500 rounded-full"}),e]},s))})]}),(0,a.jsx)("p",{className:"text-sm text-orange-700 font-medium",children:"價格已依據最新價格重新計算"})]}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6",children:(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"\uD83D\uDCA1 商品已加入購物車，您可以前往購物車查看或繼續購物"})}),(0,a.jsx)("button",{onClick:t,className:"w-full py-3 px-6 gradient-green text-white rounded-2xl font-semibold btn-hover shadow-warm",children:"我知道了"})]})]})})}function w(){let{userProfile:e}=(0,r.x)(),{addToCart:s}=(0,y._)(),[t,i]=(0,f.useState)("all"),[d,N]=(0,f.useState)([]),[w,k]=(0,f.useState)(!0),[A,z]=(0,f.useState)(null),[S,q]=(0,f.useState)(!1),[C,T]=(0,f.useState)(null),[V,I]=(0,f.useState)(null),[P,D]=(0,f.useState)(null),[F,E]=(0,f.useState)(!1),[M,O]=(0,f.useState)(null),[L,R]=(0,f.useState)(""),_=[{id:"all",name:"全部",color:"gray"},{id:"pending",name:"處理中",color:"yellow"},{id:"processing",name:"製作中",color:"blue"},{id:"completed",name:"已完成",color:"green"},{id:"cancelled",name:"已取消",color:"red"}],J=async()=>{try{k(!0);let e=await b.A.user.getOrders();N(e)}catch(e){console.error("Failed to fetch orders:",e),I("無法載入訂單列表")}finally{k(!1)}};(0,f.useEffect)(()=>{J()},[]);let $=e=>{O(e),E(!0),R("")},W=async()=>{if(M){D(M.id);try{"pending"===M.status?(await b.A.orders.cancel(M.id),alert("訂單已成功取消")):"processing"===M.status&&(await b.A.orders.requestCancel(M.id,L),alert("取消請求已發送，等待管理員確認")),await J()}catch(t){var e,s;console.error("Cancel order failed:",t),alert((null==(s=t.response)||null==(e=s.data)?void 0:e.message)||"取消訂單失敗，請稍後再試")}finally{D(null),E(!1),O(null),R("")}}},Q=async e=>{z(e.id);try{let n=await b.A.products.getAll(),d=[],c=[],o=0;for(let u of e.items){var t,a,l,r,i;let e=n.find(e=>e.id===u.productId);if(!e){d.push((null==(i=u.product)?void 0:i.name)||"未知商品");continue}if(!e.isAvailable){d.push(e.name);continue}let x=(null==(t=e.options)?void 0:t.filter(e=>"topping"===e.optionType))||[],m=(null==(a=u.customizations)?void 0:a.toppings)||[],h=m.filter(e=>x.some(s=>s.optionName===e)),p=m.filter(e=>!x.some(s=>s.optionName===e));c.push(...p);let g=e.price;h.forEach(e=>{let s=x.find(s=>s.optionName===e);s&&(g+=s.additionalPrice)});let f={sugar:(null==(l=u.customizations)?void 0:l.sugar)||"正常糖",ice:(null==(r=u.customizations)?void 0:r.ice)||"正常冰",toppings:h};for(let t=0;t<u.quantity;t++)s(e,f,g);o++}T({successCount:o,unavailableItems:d,unavailableToppings:c}),q(!0)}catch(e){console.error("重新訂購失敗:",e),alert("重新訂購失敗，請稍後再試")}finally{z(null)}},U=e=>"pending"===e||"processing"===e,H=e=>{switch(e){case"pending":return(0,a.jsx)(c,{className:"w-5 h-5 text-yellow-500"});case"processing":return(0,a.jsx)(o.A,{className:"w-5 h-5 text-blue-500"});case"completed":return(0,a.jsx)(u.A,{className:"w-5 h-5 text-green-500"});case"cancelled":return(0,a.jsx)(x,{className:"w-5 h-5 text-red-500"});default:return(0,a.jsx)(c,{className:"w-5 h-5 text-gray-500"})}},K=e=>{let s=_.find(s=>s.id===e);return s?s.name:e},Z=e=>{let s=_.find(s=>s.id===e);return s?s.color:"gray"},B="all"===t?d:d.filter(e=>e.status===t),G=e=>{let s=new Date(e);return{date:s.toLocaleDateString("zh-TW"),time:s.toLocaleTimeString("zh-TW",{hour:"2-digit",minute:"2-digit"})}};return w?(0,a.jsx)(l.A,{requireAuth:!0,requireVerification:!0,children:(0,a.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"載入訂單中..."})]})})}):V?(0,a.jsx)(l.A,{requireAuth:!0,requireVerification:!0,children:(0,a.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-red-500 mb-4",children:V}),(0,a.jsx)("button",{onClick:J,className:"px-6 py-3 bg-purple-500 text-white rounded-full hover:bg-purple-600 transition-colors",children:"重新載入"})]})})}):(0,a.jsx)(l.A,{requireAuth:!0,requireVerification:!0,children:(0,a.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,a.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(n(),{href:"/",className:"p-2 bg-purple-100 text-purple-600 rounded-full hover:bg-purple-200 transition-colors",children:(0,a.jsx)(h.A,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"w-6 h-6 text-purple-500"}),(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"我的訂單"})]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:null==e?void 0:e.displayName})]})}),(0,a.jsxs)("main",{className:"container mx-auto px-4 py-6",children:[(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"訂單記錄"}),(0,a.jsx)("p",{className:"text-gray-600",children:"查看您的訂單狀態和歷史記錄"})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("div",{className:"flex gap-2 overflow-x-auto pb-2",children:_.map(e=>(0,a.jsx)("button",{onClick:()=>i(e.id),className:"px-4 py-2 rounded-full whitespace-nowrap transition-colors ".concat(t===e.id?"bg-purple-500 text-white":"bg-white text-gray-600 hover:bg-purple-100"),children:e.name},e.id))})}),(0,a.jsx)("div",{className:"space-y-4",children:B.map(e=>{let{date:s,time:t}=G(e.createdAt);return(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-bold text-gray-800",children:["訂單 #",e.id]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[s," ",t]}),"scheduled"===e.orderType&&e.scheduledTime&&(0,a.jsxs)("p",{className:"text-sm text-orange-600",children:["預約時間: ",G(e.scheduledTime).date," ",G(e.scheduledTime).time]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[H(e.status),(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("yellow"===Z(e.status)?"bg-yellow-100 text-yellow-800":"blue"===Z(e.status)?"bg-blue-100 text-blue-800":"green"===Z(e.status)?"bg-green-100 text-green-800":"red"===Z(e.status)?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:K(e.status)})]})]}),(0,a.jsx)("div",{className:"space-y-3 mb-4",children:e.items&&e.items.map((e,s)=>(0,a.jsx)(j.A,{item:e,canEdit:!1},s))}),e.note&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-gray-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,a.jsx)("strong",{children:"備註:"})," ",e.note]})}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsx)("span",{className:"font-bold text-gray-800",children:"總計"}),(0,a.jsxs)("span",{className:"font-bold text-purple-500 text-lg",children:["NT$ ",e.totalPrice]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("button",{onClick:()=>Q(e),disabled:A===e.id,className:"w-full py-2 px-4 bg-green-100 text-green-600 rounded-lg hover:bg-green-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:A===e.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin"}),"處理中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p,{className:"w-4 h-4"}),"再點一次"]})}),U(e.status)&&(0,a.jsx)("button",{onClick:()=>$(e),disabled:P===e.id,className:"w-full py-2 px-4 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:P===e.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin"}),"處理中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x,{className:"w-4 h-4"}),"pending"===e.status?"取消訂單":"申請取消"]})})]})]})]},e.id)})}),0===B.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(m.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"暫無訂單記錄"}),(0,a.jsx)(n(),{href:"/products",className:"inline-block mt-4 px-6 py-3 bg-purple-500 text-white rounded-full hover:bg-purple-600 transition-colors",children:"開始點餐"})]})]}),F&&M&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-3xl shadow-warm max-w-md w-full p-6",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)(g.A,{className:"w-16 h-16 text-orange-500 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"pending"===M.status?"確認取消訂單":"申請取消訂單"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["訂單 #",M.id]})]}),"pending"===M.status?(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("p",{className:"text-gray-700 text-center",children:"此訂單目前處於處理中狀態，可以直接取消。"})}):(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("p",{className:"text-gray-700 mb-3",children:"此訂單正在製作中，需要管理員確認才能取消。請說明取消原因："}),(0,a.jsx)("textarea",{value:L,onChange:e=>R(e.target.value),placeholder:"請輸入取消原因...",className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500",rows:3,required:!0})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{onClick:()=>{E(!1),O(null),R("")},className:"flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors",children:"取消"}),(0,a.jsx)("button",{onClick:W,disabled:"processing"===M.status&&!L.trim(),className:"flex-1 py-3 px-4 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"pending"===M.status?"確認取消":"發送申請"})]})]})}),(0,a.jsx)(v,{isOpen:S,onClose:()=>{q(!1),T(null)},result:C})]})})}},5339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"usePathname")&&t.d(s,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},6151:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},6681:(e,s,t)=>{"use strict";t.d(s,{h:()=>n});var a=t(2115),l=t(4222),r=t(2302),i=t(6720);let n=()=>{let{isInitialized:e,isLoggedIn:s,userProfile:t}=(0,l.x)(),[n,d]=(0,a.useState)({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!0,error:null}),c=async()=>{if(!e||!s||!t)return void d(e=>({...e,loading:!1,isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null}));try{if(d(e=>({...e,loading:!0,error:null})),!r.b.hasValidToken()&&(console.log("No valid JWT token, performing backend login..."),!await (0,i.UK)()))throw Error("Failed to obtain JWT token");let e=await r.A.user.getProfile();if(e&&e.id){let s=!!e.id,t=!!e.isVerified,a=!!e.lineVerified,l=s&&t&&a;d({isRegistered:s,isPhoneVerified:t,isLineVerified:a,isFullyVerified:l,userInfo:e,loading:!1,error:null})}else d({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!1,error:null})}catch(e){var a;console.error("檢查會員狀態失敗:",e),(null==(a=e.response)?void 0:a.status)===404?d({isRegistered:!1,isPhoneVerified:!1,isLineVerified:!1,isFullyVerified:!1,userInfo:null,loading:!1,error:null}):d(s=>{var t,a;return{...s,loading:!1,error:(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||"檢查會員狀態失敗"}})}};return(0,a.useEffect)(()=>{e&&c()},[e,s,t]),{...n,refreshUserStatus:()=>{c()}}}},7550:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9053:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var a=t(5155),l=t(2115),r=t(5695),i=t(4222),n=t(6681),d=t(7312),c=t(5339),o=t(1007),u=t(6874),x=t.n(u);function m(e){let{children:s,requireAuth:t=!0,requireVerification:u=!0}=e,m=(0,r.useRouter)(),{isInitialized:h,isLoggedIn:p}=(0,i.x)(),{isFullyVerified:g,loading:f}=(0,n.h)();return((0,l.useEffect)(()=>{h&&t&&!p&&m.push("/")},[h,p,t,m]),!h||f)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center gradient-bg",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 gradient-orange rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,a.jsx)(d.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("p",{className:"text-lg font-medium text-gray-700",children:"載入中..."}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"正在檢查會員狀態"})]})}):t&&!p?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center gradient-bg",children:(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl shadow-warm p-8 max-w-md mx-4 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(c.A,{className:"w-8 h-8 text-red-500"})}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"需要登入"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"請先登入以使用此功能"}),(0,a.jsx)(x(),{href:"/",className:"w-full py-3 px-6 gradient-green text-white font-semibold rounded-2xl btn-hover shadow-warm inline-block",children:"返回首頁"})]})}):u&&p&&!g?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center gradient-bg",children:(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl shadow-warm p-8 max-w-md mx-4 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(o.A,{className:"w-8 h-8 text-blue-500"})}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"需要會員驗證"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"請先完成會員註冊和手機驗證以使用此功能"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(x(),{href:"/register",className:"w-full py-3 px-6 bg-blue-500 text-white font-semibold rounded-2xl btn-hover shadow-warm inline-block",children:"立即註冊"}),(0,a.jsx)(x(),{href:"/",className:"w-full py-3 px-6 bg-gray-200 text-gray-700 font-semibold rounded-2xl btn-hover inline-block",children:"返回首頁"})]})]})}):(0,a.jsx)(a.Fragment,{children:s})}}},e=>{var s=s=>e(e.s=s);e.O(0,[932,659,222,441,684,358],()=>s(2277)),_N_E=e.O()}]);