module.exports = app => {
  const settingsController = require('../controllers/settings.controller');
  const { authJwt } = require('../middleware');
  const router = require('express').Router();

  // 獲取所有系統設定
  router.get('/', settingsController.getAllSettings);

  // 獲取單個設定
  router.get('/:key', settingsController.getSetting);

  // 更新設定（需要管理員權限）
  router.put('/:key', [authJwt.verifyToken], settingsController.updateSetting);

  // 創建設定（需要管理員權限）
  router.post('/', [authJwt.verifyToken], settingsController.createSetting);

  app.use('/api/settings', router);
};
