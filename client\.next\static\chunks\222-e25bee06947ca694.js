"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[222],{2302:(e,o,t)=>{t.d(o,{A:()=>c,b:()=>s});var r=t(3464);let n="https://orangedrink-api2.zeabur.app/api";console.log("API_BASE_URL:",n);let a="jwt_token",s={setToken:e=>{localStorage.setItem(a,e)},getToken:()=>localStorage.getItem(a),removeToken:()=>{localStorage.removeItem(a)},hasValidToken:()=>{let e=s.getToken();if(!e)return!1;try{let o=e.split(".");if(3!==o.length)return!1;let t=JSON.parse(atob(o[1])),r=Math.floor(Date.now()/1e3);return!t.exp||t.exp>r}catch(e){return!1}}},l=r.A.create({baseURL:n,headers:{"Content-Type":"application/json"}}),i=r.A.create({baseURL:n,headers:{"Content-Type":"application/json"}});l.interceptors.request.use(e=>{let o=s.getToken();return console.log("JWT Token:",o?"".concat(o.substring(0,20),"..."):"None"),o&&(e.headers.Authorization="Bearer ".concat(o)),e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(console.error("Unauthorized, please login again"),s.removeToken()),Promise.reject(e)));let c={auth:{lineLogin:async e=>{let o=await i.post("/auth/line-login",{accessToken:e});return console.log("publicApi.post:","/auth/line-login"),console.log("publicApi.post accessToken:",e),console.log("LINE login response:",o.data),o.data.token&&(s.setToken(o.data.token),console.log("JWT token saved successfully")),o.data},register:async(e,o,t)=>(await i.post("/auth/register",{lineId:e,name:o,phone:t})).data,sendVerification:async e=>(await i.post("/auth/send-verification",{phone:e})).data,verifyPhone:async(e,o)=>(await i.post("/auth/verify-phone",{phone:e,code:o})).data,logout:async()=>{let e=await l.post("/auth/logout");return s.removeToken(),console.log("JWT token removed"),e.data},getUserLogs:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,t=arguments.length>2?arguments[2]:void 0,r=new URLSearchParams({page:e.toString(),limit:o.toString()});return t&&r.append("action",t),(await l.get("/auth/logs?".concat(r))).data}},user:{getProfile:async()=>(await l.get("/user/profile")).data,updateProfile:async e=>(await l.put("/user/profile",e)).data,getOrders:async()=>(await l.get("/user/orders")).data},products:{getAll:async()=>(await l.get("/products")).data,getCategories:async()=>(await l.get("/products/categories")).data,getByCategory:async e=>(await l.get("/products/category/".concat(e))).data,getById:async e=>(await l.get("/products/".concat(e))).data},orders:{create:async e=>(await l.post("/orders",e)).data,getById:async e=>(await l.get("/orders/".concat(e))).data,cancel:async e=>(await l.put("/orders/".concat(e,"/cancel"))).data,requestCancel:async(e,o)=>(await l.post("/orders/".concat(e,"/request-cancel"),{reason:o})).data}}},4222:(e,o,t)=>{t.d(o,{default:()=>c,x:()=>i});var r=t(5155),n=t(2115),a=t(3573),s=t(6720);let l=(0,n.createContext)({isInitialized:!1,isLoggedIn:!1,isInClient:!1,userProfile:null,error:null,isDevelopmentMode:!1}),i=()=>(0,n.useContext)(l),c=e=>{let{children:o}=e,[t,i]=(0,n.useState)(!1),[c,g]=(0,n.useState)(!1),[d,u]=(0,n.useState)(null),[f,p]=(0,n.useState)(null);(0,n.useEffect)(()=>{(async()=>{try{await (0,s.cv)(),i(!0);let e=a.A.isInClient();if(console.log("inClient",e),g(e),console.log("isLoggedIn",a.A.isLoggedIn()),a.A.isLoggedIn()){let e=await (0,s.VM)();console.log("profile",e),u(e)}}catch(e){console.error("LIFF initialization failed",e),p(e instanceof Error?e:Error("Failed to initialize LIFF"))}})()},[!1]);let I={isInitialized:t,isLoggedIn:(0,s.M3)(),isInClient:c,userProfile:d,error:f,isDevelopmentMode:!1};return(0,r.jsx)(l.Provider,{value:I,children:o})}},6720:(e,o,t)=>{t.d(o,{UK:()=>h,VM:()=>f,cv:()=>d,M3:()=>u,Rf:()=>p,Bo:()=>m,bU:()=>k});var r=t(3573),n=t(2302);let a=[{accessToken:"dev_token_andy",profile:{userId:"Udf723d52cde2495367205e5751fb8c8d",displayName:"Andy瑄",pictureUrl:"https://profile.line-scdn.net/0hhnplyM1hN0FdFR30vaZJPi1FNCt-ZG5TIyRxIjgdaSI3IyMVI3svdGwRYCQzJ3AUI3V_LzoXa3FRBkAnQ0PLdVolanBhI3UQcHV_pw"}},{accessToken:"dev_token_1",profile:{userId:"U1234567890abcdef1234567890abcdef",displayName:"測試用戶一",pictureUrl:"https://profile.line-scdn.net/0h1234567890abcdef_large"}},{accessToken:"dev_token_2",profile:{userId:"U2345678901bcdef12345678901bcdef1",displayName:"測試用戶二",pictureUrl:"https://profile.line-scdn.net/0h2345678901bcdef_large"}},{accessToken:"dev_token_3",profile:{userId:"U3456789012cdef123456789012cdef12",displayName:"測試用戶三",pictureUrl:null}}],s={isLoggedIn:!1,currentUser:null,isInClient:!0},l={init:async e=>(console.log("\uD83D\uDD27 Dev Mode: LIFF initialized with config:",e),Promise.resolve()),isLoggedIn:()=>s.isLoggedIn,isInClient:()=>s.isInClient,login:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,o=a[e]||a[0];s.isLoggedIn=!0,s.currentUser=o,console.log("\uD83D\uDD27 Dev Mode: User logged in:",o.profile.displayName),localStorage.setItem("dev_liff_user",JSON.stringify(o)),localStorage.setItem("dev_liff_logged_in","true")},logout:()=>{s.isLoggedIn=!1,s.currentUser=null,console.log("\uD83D\uDD27 Dev Mode: User logged out"),localStorage.removeItem("dev_liff_user"),localStorage.removeItem("dev_liff_logged_in")},getAccessToken:()=>s.isLoggedIn&&s.currentUser?s.currentUser.accessToken:null,getProfile:async()=>{if(!s.isLoggedIn||!s.currentUser)throw Error("User is not logged in");return s.currentUser.profile},getIDToken:()=>s.isLoggedIn?"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FjY2Vzcy5saW5lLm1lIiwic3ViIjoiVTEyMzQ1Njc4OTBhYmNkZWYxMjM0NTY3ODkwYWJjZGVmIiwiYXVkIjoiMTIzNDU2Nzg5MCIsImV4cCI6MTYzMjQ2NzIwMCwiaWF0IjoxNjMyNDYzNjAwLCJuYW1lIjoi5ris6Kmm55So5oi25LiAIiwicGljdHVyZSI6Imh0dHBzOi8vcHJvZmlsZS5saW5lLXNjZG4ubmV0LzBoMTIzNDU2Nzg5MGFiY2RlZl9sYXJnZSJ9.mock_signature":null,closeWindow:()=>{console.log("\uD83D\uDD27 Dev Mode: Close window called")},restoreState:()=>{let e=localStorage.getItem("dev_liff_user"),o="true"===localStorage.getItem("dev_liff_logged_in");e&&o&&(s.currentUser=JSON.parse(e),s.isLoggedIn=!0,console.log("\uD83D\uDD27 Dev Mode: State restored for user:",s.currentUser.profile.displayName))},switchUser:e=>{e>=0&&e<a.length&&(l.login(e),window.location.reload())},getAvailableUsers:()=>a.map((e,o)=>({index:o,name:e.profile.displayName,userId:e.profile.userId}))},i=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;try{l.login(e);let o=l.getAccessToken();if(!o)throw Error("Unable to get access token");let t=await n.A.auth.lineLogin(o);return console.log("\uD83D\uDD27 Dev Mode: Backend login successful:",t),t}catch(e){throw console.error("\uD83D\uDD27 Dev Mode: Backend login failed:",e),e}},c="2007460761-z2WnknvN",g=()=>!1,d=async()=>{try{if(g()){console.log("\uD83D\uDD27 Development mode: Using LIFF mock"),await l.init({liffId:c});return}if(!c)throw Error("LIFF ID is not defined");await r.A.init({liffId:c,withLoginOnExternalBrowser:!0}),console.log("LIFF initialization succeeded")}catch(e){throw console.error("LIFF initialization failed",e),e}},u=()=>{if(g())return l.isLoggedIn();try{if(!r.A)return console.warn("LIFF is not initialized yet"),!1;return r.A.isLoggedIn()}catch(e){return console.error("Error checking login status",e),!1}},f=async()=>{try{if(g()){if(!l.isLoggedIn())throw Error("User is not logged in");let e=await l.getProfile();return console.log("profile",e),{userId:e.userId,displayName:e.displayName,pictureUrl:e.pictureUrl}}if(!r.A.isLoggedIn())throw Error("User is not logged in");let t=await r.A.getProfile();console.log("profile:",t);var e=r.A.getDecodedIDToken();console.log("user:",e);var o=null==e?void 0:e.email;return console.log("email:",o),{userId:t.userId,displayName:t.displayName,pictureUrl:t.pictureUrl,email:o}}catch(e){throw console.error("Failed to get user profile",e),e}},p=()=>{r.A.isLoggedIn()||r.A.login()},I=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;try{if(g())return await i(e);if(!r.A.isLoggedIn())throw Error("User is not logged in to LINE");let o=v();if(!o)throw Error("Unable to get access token");let t=await n.A.auth.lineLogin(o);return console.log("Backend login successful:",t),t}catch(e){throw console.error("Backend login failed:",e),e}},h=async()=>{try{if(n.b.hasValidToken())return console.log("Valid JWT token found, no need to login"),!0;if(!u())return console.log("User not logged in to LIFF"),!1;return console.log("LIFF logged in but no JWT token, performing backend login..."),await I(),console.log("Backend login completed successfully"),!0}catch(e){return console.error("Auto backend login failed:",e),!1}},y=()=>{try{["userProfile","userPreferences","cartItems","lastOrderId","userSettings"].forEach(e=>{localStorage.removeItem(e)}),sessionStorage.clear(),console.log("User data cleared successfully")}catch(e){console.error("Error clearing user data:",e)}},w=()=>{try{["access_token","refresh_token","id_token","liff_token","auth_token"].forEach(e=>{localStorage.removeItem(e),sessionStorage.removeItem(e)}),console.log("Auth tokens cleared successfully")}catch(e){console.error("Error clearing auth tokens:",e)}},m=async()=>{try{if(!r.A.isLoggedIn())return void console.warn("User is not logged in");try{v()&&(await n.A.auth.logout(),console.log("Server logout successful"))}catch(e){console.warn("Server logout failed, continuing with client logout:",e)}y(),w(),r.A.logout(),setTimeout(()=>{window.location.href="/"},100)}catch(e){console.error("Logout failed:",e),window.location.reload()}},v=()=>g()?l.getAccessToken():r.A.isLoggedIn()&&r.A.getAccessToken()||null,k=async e=>{if(g())return void console.log("Development mode: Simulating sendMessages",e);if(!r.A.isInClient())return void console.error("sendMessages is only available in LINE app");try{await r.A.sendMessages(e),console.log("Messages sent successfully")}catch(e){throw console.error("Failed to send messages",e),e}}}}]);