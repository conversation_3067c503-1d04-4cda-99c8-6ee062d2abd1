import { io, Socket } from 'socket.io-client';
import { tokenManager } from './api';

class SocketManager {
  private socket: Socket | null = null;
  private isConnected = false;

  connect(): Promise<Socket> {
    return new Promise((resolve, reject) => {
      if (this.socket && this.isConnected) {
        resolve(this.socket);
        return;
      }

      const token = tokenManager.getToken();
      if (!token) {
        reject(new Error('No authentication token available'));
        return;
      }

      const serverUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
      
      this.socket = io(serverUrl, {
        auth: {
          token
        },
        transports: ['websocket', 'polling']
      });

      this.socket.on('connect', () => {
        console.log('Socket 連接成功');
        this.isConnected = true;
        
        // 加入客戶端房間
        this.socket?.emit('client:join');
        
        resolve(this.socket!);
      });

      this.socket.on('connect_error', (error) => {
        console.error('Socket 連接失敗:', error);
        this.isConnected = false;
        reject(error);
      });

      this.socket.on('disconnect', () => {
        console.log('Socket 已斷線');
        this.isConnected = false;
      });

      this.socket.on('error', (error) => {
        console.error('Socket 錯誤:', error);
      });
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  getSocket(): Socket | null {
    return this.socket;
  }

  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  // 訂單相關事件監聽器
  onOrderAccepted(callback: (data: any) => void) {
    this.socket?.on('order:accepted', callback);
  }

  onOrderTimeout(callback: (data: any) => void) {
    this.socket?.on('order:timeout', callback);
  }

  onOrderStatusUpdated(callback: (data: any) => void) {
    this.socket?.on('order:status_updated', callback);
  }

  // 移除事件監聽器
  offOrderAccepted() {
    this.socket?.off('order:accepted');
  }

  offOrderTimeout() {
    this.socket?.off('order:timeout');
  }

  offOrderStatusUpdated() {
    this.socket?.off('order:status_updated');
  }
}

// 創建單例實例
const socketManager = new SocketManager();

export default socketManager;
