'use client';

import { useLiff } from '@/providers/LiffProvider';
import apiService from '@/utils/api';
import { getUserProfile } from '@/utils/liff';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ArrowLeft, Coffee, User, Phone, Shield, CheckCircle } from 'lucide-react';

export default function Register() {
  const router = useRouter();
  const { isInitialized, isLoggedIn, userProfile } = useLiff();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // 表單狀態
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [step, setStep] = useState(1); // 1: 填寫資料, 2: 驗證手機
  const [sendingCode, setSendingCode] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 從 LINE 獲取用戶資料
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (isInitialized && isLoggedIn) {
        try {
          const profile = await getUserProfile();
          setName(profile.displayName || '');
          setLoading(false);
        } catch (err) {
          console.error('Failed to get user profile', err);
          setError('無法獲取用戶資料，請重新登入');
          setLoading(false);
        }
      } else if (isInitialized && !isLoggedIn) {
        router.push('/');
      }
    };

    fetchUserProfile();
  }, [isInitialized, isLoggedIn, router]);

  // 發送驗證碼
  const handleSendVerificationCode = async () => {
    if (!phone) {
      setError('請輸入手機號碼');
      return;
    }

    // 驗證手機號碼格式
    const phoneRegex = /^09\d{8}$/;
    if (!phoneRegex.test(phone)) {
      setError('請輸入有效的台灣手機號碼（例如：0912345678）');
      return;
    }

    setSendingCode(true);
    setError(null);

    try {
      // 先註冊用戶
      if (step === 1) {
        console.log('step: ', step);
        await apiService.auth.register(
          userProfile?.userId || '',
          userProfile?.email || '',
          name,
          phone
        );
      }

      // 發送驗證碼
      await apiService.auth.sendVerification(phone);
      setStep(2);
      setSuccess('驗證碼已發送到您的手機');

      // 設置倒數計時器（60秒）
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (err: any) {
      console.error('Failed to send verification code', err);
      setError(err.response?.data?.message || '發送驗證碼失敗，請稍後再試');
    } finally {
      setSendingCode(false);
    }
  };

  // 驗證手機
  const handleVerifyPhone = async () => {
    if (!verificationCode) {
      setError('請輸入驗證碼');
      return;
    }

    setVerifying(true);
    setError(null);

    try {
      await apiService.auth.verifyPhone(phone, verificationCode);
      setSuccess('手機驗證成功');

      // 延遲 2 秒後跳轉到首頁
      setTimeout(() => {
        router.push('/');
      }, 2000);
    } catch (err: any) {
      console.error('Failed to verify phone', err);
      setError(err.response?.data?.message || '驗證失敗，請檢查驗證碼是否正確');
    } finally {
      setVerifying(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-lg">載入中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen gradient-bg">
      {/* 頂部導航欄 */}
      <header className="bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Link
              href="/"
              className="p-2 bg-orange-100 text-orange-600 rounded-full hover:bg-orange-200 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 gradient-orange rounded-full flex items-center justify-center">
                <Coffee className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-800">COCO</h1>
                <p className="text-xs text-gray-600">會員註冊</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要內容 */}
      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-warm p-8 card-float">
            {/* 標題區域 */}
            <div className="text-center mb-8">
              <div className="relative inline-block mb-4">
                <div className="w-16 h-16 gradient-orange rounded-full flex items-center justify-center mx-auto">
                  {step === 1 ? (
                    <User className="w-8 h-8 text-white" />
                  ) : (
                    <Shield className="w-8 h-8 text-white" />
                  )}
                </div>
                {step === 2 && (
                  <div className="absolute -top-1 -right-1">
                    <Phone className="w-5 h-5 text-green-500 animate-pulse" />
                  </div>
                )}
              </div>
              <h1 className="text-2xl font-bold text-gray-800 mb-2">
                {step === 1 ? '會員註冊' : '手機驗證'}
              </h1>
              <p className="text-gray-600">
                {step === 1 ? '完成註冊，享受會員專屬優惠' : '請輸入收到的驗證碼'}
              </p>
            </div>

            {/* 錯誤訊息 */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-2xl mb-6 flex items-center gap-3">
                <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-xs">!</span>
                </div>
                <span className="text-sm">{error}</span>
              </div>
            )}

            {/* 成功訊息 */}
            {success && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-2xl mb-6 flex items-center gap-3">
                <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                <span className="text-sm">{success}</span>
              </div>
            )}

            {/* 步驟 1: 註冊表單 */}
            {step === 1 ? (
              <div className="space-y-6">
                {/* 姓名輸入 */}
                <div>
                  <label htmlFor="name" className="flex items-center gap-2 text-gray-700 font-medium mb-3">
                    <User className="w-4 h-4 text-orange-500" />
                    姓名
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full px-4 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all bg-gray-50 hover:bg-white"
                    placeholder="請輸入您的姓名"
                    required
                  />
                </div>

                {/* 手機號碼輸入 */}
                <div>
                  <label htmlFor="phone" className="flex items-center gap-2 text-gray-700 font-medium mb-3">
                    <Phone className="w-4 h-4 text-orange-500" />
                    手機號碼
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="例如：0912345678"
                    className="w-full px-4 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all bg-gray-50 hover:bg-white"
                    required
                  />
                </div>

                {/* 下一步按鈕 */}
                <button
                  onClick={handleSendVerificationCode}
                  disabled={sendingCode || !name || !phone}
                  className="w-full py-4 px-6 gradient-orange text-white rounded-2xl font-semibold btn-hover shadow-warm disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2"
                >
                  {sendingCode ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      處理中...
                    </>
                  ) : (
                    <>
                      下一步
                      <ArrowLeft className="w-5 h-5 rotate-180" />
                    </>
                  )}
                </button>
              </div>
            ) : (
              /* 步驟 2: 驗證碼輸入 */
              <div className="space-y-6">
                {/* 驗證碼輸入 */}
                <div>
                  <label htmlFor="code" className="flex items-center gap-2 text-gray-700 font-medium mb-3">
                    <Shield className="w-4 h-4 text-green-500" />
                    驗證碼
                  </label>
                  <input
                    type="text"
                    id="code"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    className="w-full px-4 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all bg-gray-50 hover:bg-white text-center text-2xl tracking-widest"
                    placeholder="請輸入 6 位數驗證碼"
                    maxLength={6}
                    required
                  />
                  <p className="text-sm text-gray-500 mt-2 text-center">
                    已發送驗證碼到 {phone}
                  </p>
                </div>

                {/* 按鈕組 */}
                <div className="space-y-4">
                  {/* 驗證按鈕 */}
                  <button
                    onClick={handleVerifyPhone}
                    disabled={verifying || !verificationCode}
                    className="w-full py-4 px-6 gradient-green text-white rounded-2xl font-semibold btn-hover shadow-warm disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2"
                  >
                    {verifying ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        驗證中...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="w-5 h-5" />
                        完成驗證
                      </>
                    )}
                  </button>

                  {/* 重新發送按鈕 */}
                  <button
                    onClick={handleSendVerificationCode}
                    disabled={sendingCode || countdown > 0}
                    className="w-full py-3 px-4 bg-gray-100 text-gray-600 rounded-2xl font-medium hover:bg-gray-200 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  >
                    {countdown > 0 ? (
                      <>
                        <div className="w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">{countdown}</span>
                        </div>
                        重新發送驗證碼
                      </>
                    ) : (
                      <>
                        <Phone className="w-4 h-4" />
                        重新發送驗證碼
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* 頁腳 */}
      <footer className="bg-white/50 backdrop-blur-sm p-6 text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <div className="w-6 h-6 gradient-orange rounded-full flex items-center justify-center">
            <Coffee className="w-4 h-4 text-white" />
          </div>
          <span className="text-gray-700 font-medium">COCO 飲料專門店</span>
        </div>
        <p className="text-sm text-gray-500">© 2024 COCO. All rights reserved.</p>
      </footer>
    </div>
  );
}
