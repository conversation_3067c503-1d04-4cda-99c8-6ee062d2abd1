const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

// 載入環境變量
dotenv.config();

// 創建 Express 應用程序
const app = express();

// 中間件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 簡單的路由
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to 橘子工坊蘆洲飲料店線上點餐系統 API' });
});

// 導入路由
require('./routes/auth.routes')(app);
require('./routes/user.routes')(app);
require('./routes/product.routes')(app);
require('./routes/order.routes')(app);
require('./routes/settings.routes')(app);

// 處理 404 錯誤
app.use((req, res, next) => {
  res.status(404).json({ message: '找不到請求的資源' });
});

// 處理錯誤
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: '伺服器內部錯誤',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

module.exports = app;
