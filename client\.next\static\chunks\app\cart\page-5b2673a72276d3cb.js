(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5],{1074:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var a=s(5155),r=s(9053),l=s(4222),n=s(6874),i=s.n(n),o=s(7550),c=s(7809),d=s(7712),x=s(4616);let m=(0,s(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var u=s(2115),p=s(3841),h=s(2302),g=s(5695),y=s(2322),b=s(1352),f=s(6720);async function j(e){try{let t=function(e){var t,s;let a,r=e=>new Date(e).toLocaleString("zh-TW",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"});return{type:"flex",altText:"訂單確認 - ".concat(e.orderNumber),contents:{type:"bubble",header:{type:"box",layout:"vertical",contents:[{type:"text",text:"\uD83C\uDF89 訂單確認",weight:"bold",size:"xl",color:"#ffffff"},{type:"text",text:"感謝您的訂購！",size:"sm",color:"#ffffff",margin:"sm"}],backgroundColor:"#FF6B35",paddingAll:"20px"},body:{type:"box",layout:"vertical",contents:[{type:"box",layout:"vertical",contents:[{type:"text",text:"訂單資訊",weight:"bold",size:"md",color:"#333333"},{type:"box",layout:"vertical",contents:[{type:"box",layout:"horizontal",contents:[{type:"text",text:"訂單編號",size:"sm",color:"#666666",flex:2},{type:"text",text:e.orderNumber,size:"sm",weight:"bold",flex:3,align:"end"}]},{type:"box",layout:"horizontal",contents:[{type:"text",text:"訂單時間",size:"sm",color:"#666666",flex:2},{type:"text",text:r(e.createdAt),size:"sm",flex:3,align:"end"}],margin:"sm"},{type:"box",layout:"horizontal",contents:[{type:"text",text:"訂單類型",size:"sm",color:"#666666",flex:2},{type:"text",text:(t=e.orderType,s=e.scheduledTime,"scheduled"===t&&s?"預約訂購 - ".concat(r(s)):"立即訂購"),size:"sm",flex:3,align:"end"}],margin:"sm"}],margin:"md"}]},{type:"separator",margin:"xl"},{type:"box",layout:"vertical",contents:[{type:"text",text:"訂購商品",weight:"bold",size:"md",color:"#333333"},{type:"box",layout:"vertical",contents:(a=e.items).map((e,t)=>{let s=e.customizations?Object.entries(e.customizations).filter(e=>{let[t,s]=e;return null!=s}).map(e=>{let[t,s]=e;return"addOns"===t&&Array.isArray(s)?s.map(e=>"+".concat(e.name)).join(", "):"".concat(s)}).join(" | "):"";return{type:"box",layout:"vertical",contents:[{type:"box",layout:"horizontal",contents:[{type:"text",text:"".concat(e.product.name),weight:"bold",size:"sm",flex:3},{type:"text",text:"x".concat(e.quantity),size:"sm",color:"#666666",flex:1,align:"end"},{type:"text",text:"NT$".concat(e.price),size:"sm",color:"#666666",flex:1,align:"end"}]},...s?[{type:"text",text:s,size:"xs",color:"#999999",wrap:!0,margin:"xs"}]:[],...t<a.length-1?[{type:"separator",margin:"md"}]:[]],margin:t>0?"md":"none"}}),margin:"md"}],margin:"xl"},{type:"separator",margin:"xl"},{type:"box",layout:"horizontal",contents:[{type:"text",text:"總計",weight:"bold",size:"lg",color:"#333333"},{type:"text",text:"NT$".concat(e.totalPrice),weight:"bold",size:"lg",color:"#FF6B35",align:"end"}],margin:"xl"},...e.note?[{type:"separator",margin:"xl"},{type:"box",layout:"vertical",contents:[{type:"text",text:"備註",weight:"bold",size:"sm",color:"#666666"},{type:"text",text:e.note,size:"sm",wrap:!0,margin:"sm"}],margin:"xl"}]:[]],paddingAll:"20px"},footer:{type:"box",layout:"vertical",contents:[{type:"button",action:{type:"uri",label:"查看訂單詳情",uri:"https://liff.line.me/2007460761-vMp0L0WA/orders"},style:"primary",color:"#FF6B35"}],paddingAll:"20px"}}}}(e);return await (0,f.bU)([t]),console.log("訂單確認訊息已發送"),!0}catch(e){return console.error("發送訂單確認訊息失敗:",e),!1}}function N(){let{userProfile:e}=(0,l.x)(),t=(0,g.useRouter)(),{cartItems:s,addToCart:n,updateQuantity:f,removeFromCart:N,clearCart:v,getTotalPrice:w,getTotalQuantity:z,isEmpty:k,formatOrderData:A}=(0,p._)(),[C,q]=(0,u.useState)(!1),[T,D]=(0,u.useState)("immediate"),[E,F]=(0,u.useState)(""),[I,S]=(0,u.useState)(""),[M,$]=(0,u.useState)(null),[_,O]=(0,u.useState)(!1),L=async()=>{if(!k){q(!0);try{let e=A(T,"scheduled"===T?new Date(E):void 0,I||void 0),s=await h.A.orders.create(e),a=s.data||s;try{await j(a)?console.log("訂單確認訊息已發送到 LINE"):console.log("無法發送訂單確認訊息（可能不在 LINE 環境中）")}catch(e){console.error("發送訂單確認訊息時發生錯誤:",e)}v(),alert("訂單建立成功！訂單編號：".concat(a.orderNumber)),t.push("/orders")}catch(t){var e,s;console.error("Failed to create order:",t),alert((null==(s=t.response)||null==(e=s.data)?void 0:e.message)||"訂單建立失敗，請稍後再試")}finally{q(!1)}}},P=async e=>{try{let t=(await h.A.products.getAll()).find(t=>t.id===e.productId);if(!t)return void console.error("找不到產品資訊");$({...t,cartItem:e,currentCustomizations:e.customizations}),O(!0)}catch(e){console.error("獲取產品資訊失敗:",e)}};return(0,a.jsx)(r.A,{requireAuth:!0,requireVerification:!0,children:(0,a.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,a.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(i(),{href:"/products",className:"p-2 bg-green-100 text-green-600 rounded-full hover:bg-green-200 transition-colors",children:(0,a.jsx)(o.A,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"w-6 h-6 text-green-500"}),(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"購物車"})]})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[z()," 件商品"]})]})}),(0,a.jsx)("main",{className:"container mx-auto px-4 py-6",children:k?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(c.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-600 mb-2",children:"購物車是空的"}),(0,a.jsx)("p",{className:"text-gray-500 mb-6",children:"快去選購您喜愛的飲品吧！"}),(0,a.jsx)(i(),{href:"/products",className:"inline-block px-6 py-3 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors",children:"開始點餐"})]}):(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:"商品清單"}),(0,a.jsx)("div",{className:"space-y-3",children:s.map(e=>{let t={id:e.id,productId:e.productId,quantity:e.quantity,price:e.totalPrice,customizations:e.customizations,product:{id:e.productId,name:e.name,price:e.basePrice,image:e.image}};return(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(y.A,{item:t,onEditItem:P,canEdit:!0}),(0,a.jsxs)("div",{className:"flex items-center justify-between bg-white rounded-lg p-3 border border-gray-200",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"數量控制"}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 bg-gray-50 rounded-full px-3 py-1",children:[(0,a.jsx)("button",{onClick:()=>f(e.id,e.quantity-1),className:"p-1 hover:bg-gray-200 rounded-full transition-colors",disabled:e.quantity<=1,children:(0,a.jsx)(d.A,{className:"w-4 h-4"})}),(0,a.jsx)("span",{className:"w-8 text-center text-sm font-medium",children:e.quantity}),(0,a.jsx)("button",{onClick:()=>f(e.id,e.quantity+1),className:"p-1 hover:bg-gray-200 rounded-full transition-colors",children:(0,a.jsx)(x.A,{className:"w-4 h-4"})})]}),(0,a.jsxs)("button",{onClick:()=>N(e.id),className:"flex items-center gap-1 px-3 py-1 bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors text-sm",title:"移除商品",children:[(0,a.jsx)(m,{className:"w-4 h-4"}),"移除"]})]})]})]},e.id)})})]}),(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:"訂單選項"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"訂單類型"}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"immediate",checked:"immediate"===T,onChange:e=>D(e.target.value),className:"mr-2"}),"立即訂購"]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"scheduled",checked:"scheduled"===T,onChange:e=>D(e.target.value),className:"mr-2"}),"預約訂購"]})]})]}),"scheduled"===T&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"預約時間"}),(0,a.jsx)("input",{type:"datetime-local",value:E,onChange:e=>F(e.target.value),min:(()=>{let e=new Date;return e.setMinutes(e.getMinutes()+30),e.toISOString().slice(0,16)})(),className:"w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",required:!0})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"備註"}),(0,a.jsx)("textarea",{value:I,onChange:e=>S(e.target.value),placeholder:"有什麼特殊需求嗎？",className:"w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500",rows:3})]})]}),(0,a.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("span",{className:"text-xl font-bold text-gray-800",children:"總計"}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-green-500",children:["NT$ ",w()]})]}),(0,a.jsx)("button",{onClick:L,disabled:C||"scheduled"===T&&!E,className:"w-full py-4 px-6 bg-green-500 text-white rounded-2xl font-semibold hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:C?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),"處理中..."]}):"確認訂單"})]})]})}),M&&(0,a.jsx)(b.A,{product:M,isOpen:_,onClose:()=>{O(!1),$(null)},onAddToCart:(e,t,s)=>{N(M.cartItem.id),n(e,t,s),O(!1),$(null)},isEditMode:!0,currentCustomizations:M.currentCustomizations})]})})}},2322:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(5155);function r(e){var t,s,r;let{item:l,onEditItem:n,canEdit:i=!1}=e;return(0,a.jsx)("div",{className:"border-l-4 border-purple-200 pl-4 py-3 bg-gray-50 rounded-r-lg",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[i&&n?(0,a.jsxs)("button",{onClick:()=>n(l),className:"font-medium text-purple-600 hover:text-purple-800 hover:underline transition-colors",children:[(null==(t=l.product)?void 0:t.name)||"商品"," \xd7 ",l.quantity]}):(0,a.jsxs)("span",{className:"font-medium text-gray-800",children:[(null==(s=l.product)?void 0:s.name)||"商品"," \xd7 ",l.quantity]}),i&&(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"(點擊修改)"})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["原價: NT$ ",(null==(r=l.product)?void 0:r.price)||0]}),(()=>{if(!l.customizations)return null;let e=[];return l.customizations.sugar&&e.push((0,a.jsxs)("span",{className:"text-xs text-orange-600",children:["\uD83C\uDF6F ",l.customizations.sugar]},"sugar")),l.customizations.ice&&e.push((0,a.jsxs)("span",{className:"text-xs text-blue-600",children:["\uD83E\uDDCA ",l.customizations.ice]},"ice")),e.length>0?(0,a.jsx)("div",{className:"flex items-center gap-3 mt-1",children:e}):null})(),(()=>{var e;if(!(null==(e=l.customizations)?void 0:e.toppings)||0===l.customizations.toppings.length)return null;let t=e=>({珍珠:10,椰果:10,鮮奶油:15,巧克力片:10,額外奶泡:10,布丁:12,仙草:8})[e]||10;return(0,a.jsx)("div",{className:"mt-2 space-y-1",children:l.customizations.toppings.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsxs)("span",{className:"text-green-600 flex items-center gap-1",children:[(0,a.jsx)("span",{children:"\uD83E\uDD64"}),(0,a.jsx)("span",{children:e})]}),(0,a.jsxs)("span",{className:"text-orange-500 font-medium",children:["+NT$ ",t(e)]})]},s))})})()]}),(0,a.jsxs)("div",{className:"text-right ml-4",children:[(0,a.jsxs)("div",{className:"font-semibold text-gray-800",children:["NT$ ",l.price*l.quantity]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["單價: NT$ ",l.price]})]})]})})}},7018:(e,t,s)=>{Promise.resolve().then(s.bind(s,1074))}},e=>{var t=t=>e(e.s=t);e.O(0,[932,659,222,142,441,684,358],()=>t(7018)),_N_E=e.O()}]);