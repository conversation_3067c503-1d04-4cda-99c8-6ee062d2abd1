{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/0_project/augment-projects/cocodrink-line/client/src/utils/api.ts"], "sourcesContent": ["import axios from 'axios';\n\n// API 基礎 URL\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\nconsole.log('API_BASE_URL:', API_BASE_URL);\n\n// JWT Token 管理\nconst JWT_TOKEN_KEY = 'jwt_token';\n\nexport const tokenManager = {\n  // 儲存 JWT token\n  setToken: (token: string) => {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(JWT_TOKEN_KEY, token);\n    }\n  },\n\n  // 取得 JWT token\n  getToken: (): string | null => {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(JWT_TOKEN_KEY);\n    }\n    return null;\n  },\n\n  // 移除 JWT token\n  removeToken: () => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(JWT_TOKEN_KEY);\n    }\n  },\n\n  // 檢查是否有有效的 token\n  hasValidToken: (): boolean => {\n    const token = tokenManager.getToken();\n    if (!token) return false;\n\n    try {\n      // 簡單檢查 JWT 格式\n      const parts = token.split('.');\n      if (parts.length !== 3) return false;\n\n      // 檢查是否過期（可選）\n      const payload = JSON.parse(atob(parts[1]));\n      const now = Math.floor(Date.now() / 1000);\n\n      return payload.exp ? payload.exp > now : true;\n    } catch (error) {\n      return false;\n    }\n  }\n};\n\n// 創建 axios 實例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 創建不需要 JWT 驗證的 axios 實例\nconst publicApi = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 請求攔截器：添加 JWT token 到請求頭\napi.interceptors.request.use(\n  (config) => {\n    const token = tokenManager.getToken();\n    console.log('JWT Token:', token ? `${token.substring(0, 20)}...` : 'None');\n    if (token) {\n      config.headers['Authorization'] = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 響應攔截器：處理錯誤\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    // 處理 401 未授權錯誤\n    if (error.response && error.response.status === 401) {\n      console.error('Unauthorized, please login again');\n      // 清除無效的 JWT token\n      tokenManager.removeToken();\n      // 可以在這裡觸發重新登入流程\n    }\n    return Promise.reject(error);\n  }\n);\n\n// API 服務\nconst apiService = {\n  // 身份驗證相關 API\n  auth: {\n    // LINE 登入\n    lineLogin: async (accessToken: string) => {\n      // 使用 publicApi 避免自動添加 JWT token\n      const response = await publicApi.post('/auth/line-login', { accessToken });\n      console.log('publicApi.post:', '/auth/line-login');\n      console.log('publicApi.post accessToken:', accessToken);\n      console.log('LINE login response:', response.data);\n\n      // 儲存 JWT token\n      if (response.data.token) {\n        tokenManager.setToken(response.data.token);\n        console.log('JWT token saved successfully');\n      }\n\n      return response.data;\n    },\n\n    // 註冊\n    register: async (lineId: string, email: string, name: string, phone: string) => {\n      const response = await publicApi.post('/auth/register', { lineId, email, name, phone });\n      return response.data;\n    },\n\n    // 發送手機驗證碼\n    sendVerification: async (phone: string) => {\n      const response = await publicApi.post('/auth/send-verification', { phone });\n      return response.data;\n    },\n\n    // 驗證手機驗證碼\n    verifyPhone: async (phone: string, code: string) => {\n      const response = await publicApi.post('/auth/verify-phone', { phone, code });\n      return response.data;\n    },\n\n    // 登出\n    logout: async () => {\n      const response = await api.post('/auth/logout');\n\n      // 清除 JWT token\n      tokenManager.removeToken();\n      console.log('JWT token removed');\n\n      return response.data;\n    },\n\n    // 獲取用戶日誌\n    getUserLogs: async (page = 1, limit = 20, action?: string) => {\n      const params = new URLSearchParams({\n        page: page.toString(),\n        limit: limit.toString(),\n      });\n\n      if (action) {\n        params.append('action', action);\n      }\n\n      const response = await api.get(`/auth/logs?${params}`);\n      return response.data;\n    },\n  },\n\n  // 用戶相關 API\n  user: {\n    // 獲取用戶資料\n    getProfile: async () => {\n      const response = await api.get('/user/profile');\n      return response.data;\n    },\n\n    // 更新用戶資料\n    updateProfile: async (data: any) => {\n      const response = await api.put('/user/profile', data);\n      return response.data;\n    },\n\n    // 獲取用戶訂單歷史\n    getOrders: async () => {\n      const response = await api.get('/user/orders');\n      return response.data;\n    },\n  },\n\n  // 產品相關 API\n  products: {\n    // 獲取所有產品\n    getAll: async () => {\n      const response = await api.get('/products');\n      return response.data;\n    },\n\n    // 獲取產品分類\n    getCategories: async () => {\n      const response = await api.get('/products/categories');\n      return response.data;\n    },\n\n    // 獲取特定分類的產品\n    getByCategory: async (category: string) => {\n      const response = await api.get(`/products/category/${category}`);\n      return response.data;\n    },\n\n    // 獲取特定產品詳情\n    getById: async (id: number) => {\n      const response = await api.get(`/products/${id}`);\n      return response.data;\n    },\n  },\n\n  // 訂單相關 API\n  orders: {\n    // 創建新訂單\n    create: async (orderData: any) => {\n      const response = await api.post('/orders', orderData);\n      return response.data;\n    },\n\n    // 獲取訂單詳情\n    getById: async (id: number) => {\n      const response = await api.get(`/orders/${id}`);\n      return response.data;\n    },\n\n    // 取消訂單\n    cancel: async (id: number) => {\n      const response = await api.put(`/orders/${id}/cancel`);\n      return response.data;\n    },\n\n    // 請求取消訂單（製作中狀態需要管理員確認）\n    requestCancel: async (id: number, reason?: string) => {\n      const response = await api.post(`/orders/${id}/request-cancel`, { reason });\n      return response.data;\n    },\n\n    // 重新送單\n    resubmit: async (id: number) => {\n      const response = await api.put(`/orders/${id}/resubmit`);\n      return response.data;\n    },\n  },\n\n  // 系統設定相關 API\n  settings: {\n    // 獲取所有設定\n    getAll: async () => {\n      const response = await api.get('/settings');\n      return response.data;\n    },\n\n    // 獲取單個設定\n    get: async (key: string) => {\n      const response = await api.get(`/settings/${key}`);\n      return response.data;\n    },\n\n    // 更新設定\n    update: async (key: string, value: any, description?: string) => {\n      const response = await api.put(`/settings/${key}`, { value, description });\n      return response.data;\n    },\n\n    // 創建設定\n    create: async (key: string, value: any, description?: string, type?: string) => {\n      const response = await api.post('/settings', { key, value, description, type });\n      return response.data;\n    },\n  },\n};\n\nexport default apiService;\n"], "names": [], "mappings": ";;;;AAGqB;AAHrB;;AAEA,aAAa;AACb,MAAM,eAAe,+EAAmC;AACxD,QAAQ,GAAG,CAAC,iBAAiB;AAE7B,eAAe;AACf,MAAM,gBAAgB;AAEf,MAAM,eAAe;IAC1B,eAAe;IACf,UAAU,CAAC;QACT,wCAAmC;YACjC,aAAa,OAAO,CAAC,eAAe;QACtC;IACF;IAEA,eAAe;IACf,UAAU;QACR,wCAAmC;YACjC,OAAO,aAAa,OAAO,CAAC;QAC9B;;IAEF;IAEA,eAAe;IACf,aAAa;QACX,wCAAmC;YACjC,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,iBAAiB;IACjB,eAAe;QACb,MAAM,QAAQ,aAAa,QAAQ;QACnC,IAAI,CAAC,OAAO,OAAO;QAEnB,IAAI;YACF,cAAc;YACd,MAAM,QAAQ,MAAM,KAAK,CAAC;YAC1B,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;YAE/B,aAAa;YACb,MAAM,UAAU,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE;YACxC,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;YAEpC,OAAO,QAAQ,GAAG,GAAG,QAAQ,GAAG,GAAG,MAAM;QAC3C,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAEA,cAAc;AACd,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,yBAAyB;AACzB,MAAM,YAAY,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,0BAA0B;AAC1B,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,MAAM,QAAQ,aAAa,QAAQ;IACnC,QAAQ,GAAG,CAAC,cAAc,QAAQ,GAAG,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;IACnE,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACrD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,aAAa;AACb,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,eAAe;IACf,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;QACnD,QAAQ,KAAK,CAAC;QACd,kBAAkB;QAClB,aAAa,WAAW;IACxB,gBAAgB;IAClB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,SAAS;AACT,MAAM,aAAa;IACjB,aAAa;IACb,MAAM;QACJ,UAAU;QACV,WAAW,OAAO;YAChB,gCAAgC;YAChC,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,oBAAoB;gBAAE;YAAY;YACxE,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,QAAQ,GAAG,CAAC,wBAAwB,SAAS,IAAI;YAEjD,eAAe;YACf,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;gBACvB,aAAa,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK;gBACzC,QAAQ,GAAG,CAAC;YACd;YAEA,OAAO,SAAS,IAAI;QACtB;QAEA,KAAK;QACL,UAAU,OAAO,QAAgB,OAAe,MAAc;YAC5D,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,kBAAkB;gBAAE;gBAAQ;gBAAO;gBAAM;YAAM;YACrF,OAAO,SAAS,IAAI;QACtB;QAEA,UAAU;QACV,kBAAkB,OAAO;YACvB,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,2BAA2B;gBAAE;YAAM;YACzE,OAAO,SAAS,IAAI;QACtB;QAEA,UAAU;QACV,aAAa,OAAO,OAAe;YACjC,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,sBAAsB;gBAAE;gBAAO;YAAK;YAC1E,OAAO,SAAS,IAAI;QACtB;QAEA,KAAK;QACL,QAAQ;YACN,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC;YAEhC,eAAe;YACf,aAAa,WAAW;YACxB,QAAQ,GAAG,CAAC;YAEZ,OAAO,SAAS,IAAI;QACtB;QAEA,SAAS;QACT,aAAa,OAAO,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE;YACxC,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,KAAK,QAAQ;gBACnB,OAAO,MAAM,QAAQ;YACvB;YAEA,IAAI,QAAQ;gBACV,OAAO,MAAM,CAAC,UAAU;YAC1B;YAEA,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ;YACrD,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,WAAW;IACX,MAAM;QACJ,SAAS;QACT,YAAY;YACV,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB;QAEA,SAAS;QACT,eAAe,OAAO;YACpB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,iBAAiB;YAChD,OAAO,SAAS,IAAI;QACtB;QAEA,WAAW;QACX,WAAW;YACT,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,WAAW;IACX,UAAU;QACR,SAAS;QACT,QAAQ;YACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB;QAEA,SAAS;QACT,eAAe;YACb,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB;QAEA,YAAY;QACZ,eAAe,OAAO;YACpB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;YAC/D,OAAO,SAAS,IAAI;QACtB;QAEA,WAAW;QACX,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;YAChD,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,WAAW;IACX,QAAQ;QACN,QAAQ;QACR,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;YAC3C,OAAO,SAAS,IAAI;QACtB;QAEA,SAAS;QACT,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI;YAC9C,OAAO,SAAS,IAAI;QACtB;QAEA,OAAO;QACP,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC;YACrD,OAAO,SAAS,IAAI;QACtB;QAEA,uBAAuB;QACvB,eAAe,OAAO,IAAY;YAChC,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,QAAQ,EAAE,GAAG,eAAe,CAAC,EAAE;gBAAE;YAAO;YACzE,OAAO,SAAS,IAAI;QACtB;QAEA,OAAO;QACP,UAAU,OAAO;YACf,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,GAAG,SAAS,CAAC;YACvD,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,aAAa;IACb,UAAU;QACR,SAAS;QACT,QAAQ;YACN,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB;QAEA,SAAS;QACT,KAAK,OAAO;YACV,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,KAAK;YACjD,OAAO,SAAS,IAAI;QACtB;QAEA,OAAO;QACP,QAAQ,OAAO,KAAa,OAAY;YACtC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE;gBAAE;gBAAO;YAAY;YACxE,OAAO,SAAS,IAAI;QACtB;QAEA,OAAO;QACP,QAAQ,OAAO,KAAa,OAAY,aAAsB;YAC5D,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;gBAAE;gBAAK;gBAAO;gBAAa;YAAK;YAC7E,OAAO,SAAS,IAAI;QACtB;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/0_project/augment-projects/cocodrink-line/client/src/utils/liff-dev.ts"], "sourcesContent": ["// 開發模式的 LIFF 模擬器\nimport apiService, { tokenManager } from './api';\n\n// 模擬的 LIFF 用戶資料\nconst mockUsers = [\n  {\n    accessToken: 'dev_token_andy',\n    profile: {\n      userId: 'Udf723d52cde2495367205e5751fb8c8d',\n      displayName: 'Andy瑄',\n      pictureUrl: 'https://profile.line-scdn.net/0hhnplyM1hN0FdFR30vaZJPi1FNCt-ZG5TIyRxIjgdaSI3IyMVI3svdGwRYCQzJ3AUI3V_LzoXa3FRBkAnQ0PLdVolanBhI3UQcHV_pw'\n    }\n  },\n  {\n    accessToken: 'dev_token_1',\n    profile: {\n      userId: 'U1234567890abcdef1234567890abcdef',\n      displayName: '測試用戶一',\n      pictureUrl: 'https://profile.line-scdn.net/0h1234567890abcdef_large'\n    }\n  },\n  {\n    accessToken: 'dev_token_2',\n    profile: {\n      userId: 'U2345678901bcdef12345678901bcdef1',\n      displayName: '測試用戶二',\n      pictureUrl: 'https://profile.line-scdn.net/0h2345678901bcdef_large'\n    }\n  },\n  {\n    accessToken: 'dev_token_3',\n    profile: {\n      userId: 'U3456789012cdef123456789012cdef12',\n      displayName: '測試用戶三',\n      pictureUrl: null\n    }\n  }\n];\n\n// 開發模式狀態\nlet devModeState = {\n  isLoggedIn: false,\n  currentUser: null as any,\n  isInClient: true // 模擬在 LINE 應用內\n};\n\n// 開發模式的 LIFF 模擬器\nexport const liffDevMock = {\n  // 初始化\n  init: async (config: any) => {\n    console.log('🔧 Dev Mode: LIFF initialized with config:', config);\n    return Promise.resolve();\n  },\n\n  // 檢查是否已登入\n  isLoggedIn: () => {\n    return devModeState.isLoggedIn;\n  },\n\n  // 檢查是否在 LINE 應用內\n  isInClient: () => {\n    return devModeState.isInClient;\n  },\n\n  // 模擬登入\n  login: (userIndex = 0) => {\n    const user = mockUsers[userIndex] || mockUsers[0];\n    devModeState.isLoggedIn = true;\n    devModeState.currentUser = user;\n    console.log('🔧 Dev Mode: User logged in:', user.profile.displayName);\n\n    // 儲存到 localStorage 以便重新載入後保持狀態\n    localStorage.setItem('dev_liff_user', JSON.stringify(user));\n    localStorage.setItem('dev_liff_logged_in', 'true');\n  },\n\n  // 模擬登出\n  logout: () => {\n    devModeState.isLoggedIn = false;\n    devModeState.currentUser = null;\n    console.log('🔧 Dev Mode: User logged out');\n\n    // 清除 localStorage\n    localStorage.removeItem('dev_liff_user');\n    localStorage.removeItem('dev_liff_logged_in');\n  },\n\n  // 取得 access token\n  getAccessToken: () => {\n    if (!devModeState.isLoggedIn || !devModeState.currentUser) {\n      return null;\n    }\n    return devModeState.currentUser.accessToken;\n  },\n\n  // 取得用戶資料\n  getProfile: async () => {\n    if (!devModeState.isLoggedIn || !devModeState.currentUser) {\n      throw new Error('User is not logged in');\n    }\n    return devModeState.currentUser.profile;\n  },\n\n  // 取得 ID Token (模擬)\n  getIDToken: () => {\n    if (!devModeState.isLoggedIn) {\n      return null;\n    }\n    // 返回一個模擬的 JWT token\n    return 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FjY2Vzcy5saW5lLm1lIiwic3ViIjoiVTEyMzQ1Njc4OTBhYmNkZWYxMjM0NTY3ODkwYWJjZGVmIiwiYXVkIjoiMTIzNDU2Nzg5MCIsImV4cCI6MTYzMjQ2NzIwMCwiaWF0IjoxNjMyNDYzNjAwLCJuYW1lIjoi5ris6Kmm55So5oi25LiAIiwicGljdHVyZSI6Imh0dHBzOi8vcHJvZmlsZS5saW5lLXNjZG4ubmV0LzBoMTIzNDU2Nzg5MGFiY2RlZl9sYXJnZSJ9.mock_signature';\n  },\n\n  // 關閉視窗\n  closeWindow: () => {\n    console.log('🔧 Dev Mode: Close window called');\n  },\n\n  // 恢復狀態（從 localStorage）\n  restoreState: () => {\n    const savedUser = localStorage.getItem('dev_liff_user');\n    const isLoggedIn = localStorage.getItem('dev_liff_logged_in') === 'true';\n\n    if (savedUser && isLoggedIn) {\n      devModeState.currentUser = JSON.parse(savedUser);\n      devModeState.isLoggedIn = true;\n      console.log('🔧 Dev Mode: State restored for user:', devModeState.currentUser.profile.displayName);\n    }\n  },\n\n  // 切換用戶（開發用）\n  switchUser: (userIndex: number) => {\n    if (userIndex >= 0 && userIndex < mockUsers.length) {\n      liffDevMock.login(userIndex);\n      window.location.reload(); // 重新載入以更新狀態\n    }\n  },\n\n  // 取得所有可用的測試用戶\n  getAvailableUsers: () => {\n    return mockUsers.map((user, index) => ({\n      index,\n      name: user.profile.displayName,\n      userId: user.profile.userId\n    }));\n  }\n};\n\n// 開發模式的完整登入流程\nexport const devLoginWithBackend = async (userIndex = 0) => {\n  try {\n    // 模擬 LIFF 登入\n    liffDevMock.login(userIndex);\n\n    // 取得 access token\n    const accessToken = liffDevMock.getAccessToken();\n    if (!accessToken) {\n      throw new Error('Unable to get access token');\n    }\n\n    // 調用後端 API 進行登入\n    const response = await apiService.auth.lineLogin(accessToken);\n\n    console.log('🔧 Dev Mode: Backend login successful:', response);\n    return response;\n  } catch (error) {\n    console.error('🔧 Dev Mode: Backend login failed:', error);\n    throw error;\n  }\n};\n\n// 初始化開發模式\nexport const initDevMode = () => {\n  // 恢復之前的狀態\n  liffDevMock.restoreState();\n\n  // 在開發工具中添加全域變數以便調試\n  if (typeof window !== 'undefined') {\n    (window as any).liffDev = liffDevMock;\n    (window as any).devLogin = devLoginWithBackend;\n\n    console.log('🔧 Dev Mode initialized!');\n    console.log('Available commands:');\n    console.log('- window.liffDev.switchUser(0-2): 切換測試用戶');\n    console.log('- window.devLogin(userIndex): 執行完整登入流程');\n    console.log('- window.liffDev.getAvailableUsers(): 查看可用用戶');\n  }\n};\n\nexport default liffDevMock;\n"], "names": [], "mappings": "AAAA,iBAAiB;;;;;;;AACjB;;AAEA,gBAAgB;AAChB,MAAM,YAAY;IAChB;QACE,aAAa;QACb,SAAS;YACP,QAAQ;YACR,aAAa;YACb,YAAY;QACd;IACF;IACA;QACE,aAAa;QACb,SAAS;YACP,QAAQ;YACR,aAAa;YACb,YAAY;QACd;IACF;IACA;QACE,aAAa;QACb,SAAS;YACP,QAAQ;YACR,aAAa;YACb,YAAY;QACd;IACF;IACA;QACE,aAAa;QACb,SAAS;YACP,QAAQ;YACR,aAAa;YACb,YAAY;QACd;IACF;CACD;AAED,SAAS;AACT,IAAI,eAAe;IACjB,YAAY;IACZ,aAAa;IACb,YAAY,KAAK,eAAe;AAClC;AAGO,MAAM,cAAc;IACzB,MAAM;IACN,MAAM,OAAO;QACX,QAAQ,GAAG,CAAC,8CAA8C;QAC1D,OAAO,QAAQ,OAAO;IACxB;IAEA,UAAU;IACV,YAAY;QACV,OAAO,aAAa,UAAU;IAChC;IAEA,iBAAiB;IACjB,YAAY;QACV,OAAO,aAAa,UAAU;IAChC;IAEA,OAAO;IACP,OAAO,CAAC,YAAY,CAAC;QACnB,MAAM,OAAO,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,EAAE;QACjD,aAAa,UAAU,GAAG;QAC1B,aAAa,WAAW,GAAG;QAC3B,QAAQ,GAAG,CAAC,gCAAgC,KAAK,OAAO,CAAC,WAAW;QAEpE,+BAA+B;QAC/B,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;QACrD,aAAa,OAAO,CAAC,sBAAsB;IAC7C;IAEA,OAAO;IACP,QAAQ;QACN,aAAa,UAAU,GAAG;QAC1B,aAAa,WAAW,GAAG;QAC3B,QAAQ,GAAG,CAAC;QAEZ,kBAAkB;QAClB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;IAC1B;IAEA,kBAAkB;IAClB,gBAAgB;QACd,IAAI,CAAC,aAAa,UAAU,IAAI,CAAC,aAAa,WAAW,EAAE;YACzD,OAAO;QACT;QACA,OAAO,aAAa,WAAW,CAAC,WAAW;IAC7C;IAEA,SAAS;IACT,YAAY;QACV,IAAI,CAAC,aAAa,UAAU,IAAI,CAAC,aAAa,WAAW,EAAE;YACzD,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,aAAa,WAAW,CAAC,OAAO;IACzC;IAEA,mBAAmB;IACnB,YAAY;QACV,IAAI,CAAC,aAAa,UAAU,EAAE;YAC5B,OAAO;QACT;QACA,oBAAoB;QACpB,OAAO;IACT;IAEA,OAAO;IACP,aAAa;QACX,QAAQ,GAAG,CAAC;IACd;IAEA,uBAAuB;IACvB,cAAc;QACZ,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,MAAM,aAAa,aAAa,OAAO,CAAC,0BAA0B;QAElE,IAAI,aAAa,YAAY;YAC3B,aAAa,WAAW,GAAG,KAAK,KAAK,CAAC;YACtC,aAAa,UAAU,GAAG;YAC1B,QAAQ,GAAG,CAAC,yCAAyC,aAAa,WAAW,CAAC,OAAO,CAAC,WAAW;QACnG;IACF;IAEA,YAAY;IACZ,YAAY,CAAC;QACX,IAAI,aAAa,KAAK,YAAY,UAAU,MAAM,EAAE;YAClD,YAAY,KAAK,CAAC;YAClB,OAAO,QAAQ,CAAC,MAAM,IAAI,YAAY;QACxC;IACF;IAEA,cAAc;IACd,mBAAmB;QACjB,OAAO,UAAU,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBACrC;gBACA,MAAM,KAAK,OAAO,CAAC,WAAW;gBAC9B,QAAQ,KAAK,OAAO,CAAC,MAAM;YAC7B,CAAC;IACH;AACF;AAGO,MAAM,sBAAsB,OAAO,YAAY,CAAC;IACrD,IAAI;QACF,aAAa;QACb,YAAY,KAAK,CAAC;QAElB,kBAAkB;QAClB,MAAM,cAAc,YAAY,cAAc;QAC9C,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,gBAAgB;QAChB,MAAM,WAAW,MAAM,sHAAA,CAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;QAEjD,QAAQ,GAAG,CAAC,0CAA0C;QACtD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,UAAU;IACV,YAAY,YAAY;IAExB,mBAAmB;IACnB,wCAAmC;QAChC,OAAe,OAAO,GAAG;QACzB,OAAe,QAAQ,GAAG;QAE3B,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;IACd;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/0_project/augment-projects/cocodrink-line/client/src/utils/liff.ts"], "sourcesContent": ["import liff from '@line/liff';\nimport apiService, { tokenManager } from './api';\nimport { liffDevMock, devLoginWithBackend } from './liff-dev';\n\n// LIFF ID\nconst LIFF_ID = typeof window !== 'undefined'\n  ? process.env.NEXT_PUBLIC_LIFF_ID || '2007460761-vMp0L0WA'  // 客戶端\n  : '2007460761-vMp0L0WA';  // 伺服器端\n\n// 檢查是否為開發模式\nconst isDevMode = () => {\n  return process.env.NODE_ENV === 'development' &&\n         typeof window !== 'undefined' &&\n         window.location.hostname === 'localhost';\n};\n\n// LIFF 初始化\nexport const initializeLiff = async (): Promise<void> => {\n  try {\n    // 開發模式：使用模擬器\n    if (isDevMode()) {\n      console.log('🔧 Development mode: Using LIFF mock');\n      await liffDevMock.init({ liffId: LIFF_ID });\n      return;\n    }\n\n    if (!LIFF_ID) {\n      throw new Error('LIFF ID is not defined');\n    }\n\n    await liff.init({\n      liffId: LIFF_ID,\n      withLoginOnExternalBrowser: true, // 在外部瀏覽器中自動登入\n    });\n    console.log('LIFF initialization succeeded');\n  } catch (error) {\n    console.error('LIFF initialization failed', error);\n    throw error;\n  }\n};\n\n// 檢查是否在 LIFF 環境中\nexport const isInLiffBrowser = (): boolean => {\n  if (typeof window === 'undefined') return false;\n\n  // 開發模式\n  if (isDevMode()) {\n    return liffDevMock.isInClient();\n  }\n\n  return liff.isInClient();\n};\n\n// 檢查用戶是否已登入\nexport const isLoggedIn = (): boolean => {\n  if (typeof window === 'undefined') return false;\n\n  // 開發模式\n  if (isDevMode()) {\n    return liffDevMock.isLoggedIn();\n  }\n\n  try {\n    // 確保 LIFF 已初始化\n    if (!liff) {\n      console.warn('LIFF is not initialized yet');\n      return false;\n    }\n    return liff.isLoggedIn();\n  } catch (error) {\n    console.error('Error checking login status', error);\n    return false;\n  }\n};\n\n// 獲取用戶資料\nexport const getUserProfile = async () => {\n  try {\n    // 開發模式\n    if (isDevMode()) {\n      if (!liffDevMock.isLoggedIn()) {\n        throw new Error('User is not logged in');\n      }\n      const profile = await liffDevMock.getProfile();\n      console.log('profile', profile);\n      return {\n        userId: profile.userId,\n        displayName: profile.displayName,\n        pictureUrl: profile.pictureUrl,\n        email: \"<EMAIL>\",\n      };\n    }\n\n    if (!liff.isLoggedIn()) {\n      throw new Error('User is not logged in');\n    }\n\n    // 獲取用戶資料\n    const profile = await liff.getProfile();\n    console.log(\"profile:\", profile);\n    // 取得使用者 email\n    // 後台的 Email address permission 要是「Applied」\n    // LIFF 的設定，Scopes 的「email*」要打勾\n    // 使用者在登入時，「電子郵件帳號」也要是「許可」的\n    var user = liff.getDecodedIDToken();\n    console.log(\"user:\", user);\n    var email = user?.email;\n    console.log(\"email:\", email);\n    return {\n      userId: profile.userId,\n      displayName: profile.displayName,\n      pictureUrl: profile.pictureUrl,\n      email: email,\n    };\n  } catch (error) {\n    console.error('Failed to get user profile', error);\n    throw error;\n  }\n};\n\n// LINE 登入\nexport const login = (): void => {\n  if (!liff.isLoggedIn()) {\n    liff.login();\n  }\n};\n\n// 完整的 LINE 登入流程（包含後端 API 調用）\nexport const loginWithBackend = async (userIndex = 0) => {\n  try {\n    // 開發模式：使用專用的開發登入流程\n    if (isDevMode()) {\n      return await devLoginWithBackend(userIndex);\n    }\n\n    if (!liff.isLoggedIn()) {\n      throw new Error('User is not logged in to LINE');\n    }\n\n    // 取得 access token\n    const accessToken = getAccessToken();\n    if (!accessToken) {\n      throw new Error('Unable to get access token');\n    }\n\n    // 調用後端 API 進行登入\n    const response = await apiService.auth.lineLogin(accessToken);\n\n    console.log('Backend login successful:', response);\n    return response;\n  } catch (error) {\n    console.error('Backend login failed:', error);\n    throw error;\n  }\n};\n\n// 檢查並自動執行後端登入（如果需要）\nexport const ensureBackendLogin = async () => {\n  try {\n    // 檢查是否有有效的 JWT token\n    if (tokenManager.hasValidToken()) {\n      console.log('Valid JWT token found, no need to login');\n      return true;\n    }\n\n    // 檢查 LIFF 是否已登入\n    if (!isLoggedIn()) {\n      console.log('User not logged in to LIFF');\n      return false;\n    }\n\n    console.log('LIFF logged in but no JWT token, performing backend login...');\n\n    // 執行後端登入\n    await loginWithBackend();\n\n    console.log('Backend login completed successfully');\n    return true;\n  } catch (error) {\n    console.error('Auto backend login failed:', error);\n    return false;\n  }\n};\n\n// 清理用戶相關的本地資料\nexport const clearUserData = (): void => {\n  try {\n    // 清理 localStorage 中的用戶相關資料\n    const keysToRemove = [\n      'userProfile',\n      'userPreferences',\n      'cartItems',\n      'lastOrderId',\n      'userSettings'\n    ];\n\n    keysToRemove.forEach(key => {\n      localStorage.removeItem(key);\n    });\n\n    // 清理 sessionStorage\n    sessionStorage.clear();\n\n    console.log('User data cleared successfully');\n  } catch (error) {\n    console.error('Error clearing user data:', error);\n  }\n};\n\n// 清理所有認證相關的 token 和資料\nexport const clearAuthTokens = (): void => {\n  try {\n    // 清理可能儲存在 localStorage 中的 token\n    const tokenKeys = [\n      'access_token',\n      'refresh_token',\n      'id_token',\n      'liff_token',\n      'auth_token'\n    ];\n\n    tokenKeys.forEach(key => {\n      localStorage.removeItem(key);\n      sessionStorage.removeItem(key);\n    });\n\n    console.log('Auth tokens cleared successfully');\n  } catch (error) {\n    console.error('Error clearing auth tokens:', error);\n  }\n};\n\n// LINE 登出（基本版本，保持向後兼容）\nexport const logout = (): void => {\n  if (liff.isLoggedIn()) {\n    liff.logout();\n    window.location.reload();\n  }\n};\n\n// LINE 登出（完整版本，包含資料清理和 API 調用）\nexport const logoutWithCleanup = async (): Promise<void> => {\n  try {\n    if (!liff.isLoggedIn()) {\n      console.warn('User is not logged in');\n      return;\n    }\n\n    // 1. 先調用伺服器端登出 API（如果有 token）\n    try {\n      const token = getAccessToken();\n      if (token) {\n        await apiService.auth.logout();\n        console.log('Server logout successful');\n      }\n    } catch (apiError) {\n      console.warn('Server logout failed, continuing with client logout:', apiError);\n      // 即使伺服器登出失敗，也繼續執行客戶端登出\n    }\n\n    // 2. 清理本地資料和 token\n    clearUserData();\n    clearAuthTokens();\n\n    // 3. 執行 LIFF 登出（這會清除 LIFF 的 access token）\n    liff.logout();\n\n    // 4. 短暫延遲後重新載入頁面，確保登出完成\n    setTimeout(() => {\n      window.location.href = '/';\n    }, 100);\n\n  } catch (error) {\n    console.error('Logout failed:', error);\n    // 即使出錯也嘗試重新載入頁面\n    window.location.reload();\n  }\n};\n\n// 關閉 LIFF 視窗\nexport const closeLiff = (): void => {\n  liff.closeWindow();\n};\n\n// 獲取 LINE 用戶的 ID Token\nexport const getIdToken = (): string | null => {\n  if (typeof window === 'undefined') return null;\n  if (!liff.isLoggedIn()) {\n    return null;\n  }\n  return liff.getIDToken() || null;\n};\n\n// 獲取 LINE 用戶的 Access Token\nexport const getAccessToken = (): string | null => {\n  if (typeof window === 'undefined') return null;\n\n  // 開發模式\n  if (isDevMode()) {\n    return liffDevMock.getAccessToken();\n  }\n\n  if (!liff.isLoggedIn()) {\n    return null;\n  }\n  return liff.getAccessToken() || null;\n};\n\n// 打開外部連結\nexport const openExternalLink = (url: string): void => {\n  liff.openWindow({\n    url,\n    external: true,\n  });\n};\n\n// 分享訊息\nexport const shareMessage = async (messages: any[]): Promise<void> => {\n  if (!liff.isInClient()) {\n    console.error('Share message is only available in LINE app');\n    return;\n  }\n\n  try {\n    await liff.shareTargetPicker(messages);\n  } catch (error) {\n    console.error('Failed to share message', error);\n    throw error;\n  }\n};\n\n// 發送訊息到 LINE 聊天室\nexport const sendMessages = async (messages: any[]): Promise<void> => {\n  if (typeof window === 'undefined') {\n    console.error('sendMessages is only available in browser environment');\n    return;\n  }\n\n  // 開發模式\n  if (isDevMode()) {\n    console.log('Development mode: Simulating sendMessages', messages);\n    return;\n  }\n\n  if (!liff.isInClient()) {\n    console.error('sendMessages is only available in LINE app');\n    return;\n  }\n\n  try {\n    await liff.sendMessages(messages);\n    console.log('Messages sent successfully');\n  } catch (error) {\n    console.error('Failed to send messages', error);\n    throw error;\n  }\n};\n\nexport default {\n  initializeLiff,\n  isInLiffBrowser,\n  isLoggedIn,\n  getUserProfile,\n  login,\n  loginWithBackend,\n  ensureBackendLogin,\n  logout,\n  logoutWithCleanup,\n  clearUserData,\n  clearAuthTokens,\n  closeLiff,\n  getIdToken,\n  getAccessToken,\n  openExternalLink,\n  shareMessage,\n  sendMessages,\n};\n\n\n\n\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAMI;AANJ;AACA;AACA;;;;AAEA,UAAU;AACV,MAAM,UAAU,uCACZ,2DAAmC,sBAAuB,MAAM;yCACxC,OAAO;AAEnC,YAAY;AACZ,MAAM,YAAY;IAChB,OAAO,oDAAyB,iBACzB,aAAkB,eAClB,OAAO,QAAQ,CAAC,QAAQ,KAAK;AACtC;AAGO,MAAM,iBAAiB;IAC5B,IAAI;QACF,aAAa;QACb,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC;YACZ,MAAM,8HAAA,CAAA,cAAW,CAAC,IAAI,CAAC;gBAAE,QAAQ;YAAQ;YACzC;QACF;QAEA,uCAAc;;QAEd;QAEA,MAAM,2IAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YACd,QAAQ;YACR,4BAA4B;QAC9B;QACA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,MAAM,kBAAkB;IAC7B,uCAAmC;;IAAY;IAE/C,OAAO;IACP,IAAI,aAAa;QACf,OAAO,8HAAA,CAAA,cAAW,CAAC,UAAU;IAC/B;IAEA,OAAO,2IAAA,CAAA,UAAI,CAAC,UAAU;AACxB;AAGO,MAAM,aAAa;IACxB,uCAAmC;;IAAY;IAE/C,OAAO;IACP,IAAI,aAAa;QACf,OAAO,8HAAA,CAAA,cAAW,CAAC,UAAU;IAC/B;IAEA,IAAI;QACF,eAAe;QACf,IAAI,CAAC,2IAAA,CAAA,UAAI,EAAE;YACT,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QACA,OAAO,2IAAA,CAAA,UAAI,CAAC,UAAU;IACxB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAGO,MAAM,iBAAiB;IAC5B,IAAI;QACF,OAAO;QACP,IAAI,aAAa;YACf,IAAI,CAAC,8HAAA,CAAA,cAAW,CAAC,UAAU,IAAI;gBAC7B,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,UAAU,MAAM,8HAAA,CAAA,cAAW,CAAC,UAAU;YAC5C,QAAQ,GAAG,CAAC,WAAW;YACvB,OAAO;gBACL,QAAQ,QAAQ,MAAM;gBACtB,aAAa,QAAQ,WAAW;gBAChC,YAAY,QAAQ,UAAU;gBAC9B,OAAO;YACT;QACF;QAEA,IAAI,CAAC,2IAAA,CAAA,UAAI,CAAC,UAAU,IAAI;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,SAAS;QACT,MAAM,UAAU,MAAM,2IAAA,CAAA,UAAI,CAAC,UAAU;QACrC,QAAQ,GAAG,CAAC,YAAY;QACxB,cAAc;QACd,2CAA2C;QAC3C,+BAA+B;QAC/B,2BAA2B;QAC3B,IAAI,OAAO,2IAAA,CAAA,UAAI,CAAC,iBAAiB;QACjC,QAAQ,GAAG,CAAC,SAAS;QACrB,IAAI,QAAQ,MAAM;QAClB,QAAQ,GAAG,CAAC,UAAU;QACtB,OAAO;YACL,QAAQ,QAAQ,MAAM;YACtB,aAAa,QAAQ,WAAW;YAChC,YAAY,QAAQ,UAAU;YAC9B,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,MAAM,QAAQ;IACnB,IAAI,CAAC,2IAAA,CAAA,UAAI,CAAC,UAAU,IAAI;QACtB,2IAAA,CAAA,UAAI,CAAC,KAAK;IACZ;AACF;AAGO,MAAM,mBAAmB,OAAO,YAAY,CAAC;IAClD,IAAI;QACF,mBAAmB;QACnB,IAAI,aAAa;YACf,OAAO,MAAM,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE;QACnC;QAEA,IAAI,CAAC,2IAAA,CAAA,UAAI,CAAC,UAAU,IAAI;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,kBAAkB;QAClB,MAAM,cAAc;QACpB,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,gBAAgB;QAChB,MAAM,WAAW,MAAM,sHAAA,CAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;QAEjD,QAAQ,GAAG,CAAC,6BAA6B;QACzC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB;IAChC,IAAI;QACF,qBAAqB;QACrB,IAAI,sHAAA,CAAA,eAAY,CAAC,aAAa,IAAI;YAChC,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,gBAAgB;QAChB,IAAI,CAAC,cAAc;YACjB,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC;QAEZ,SAAS;QACT,MAAM;QAEN,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAGO,MAAM,gBAAgB;IAC3B,IAAI;QACF,2BAA2B;QAC3B,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;SACD;QAED,aAAa,OAAO,CAAC,CAAA;YACnB,aAAa,UAAU,CAAC;QAC1B;QAEA,oBAAoB;QACpB,eAAe,KAAK;QAEpB,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;AACF;AAGO,MAAM,kBAAkB;IAC7B,IAAI;QACF,gCAAgC;QAChC,MAAM,YAAY;YAChB;YACA;YACA;YACA;YACA;SACD;QAED,UAAU,OAAO,CAAC,CAAA;YAChB,aAAa,UAAU,CAAC;YACxB,eAAe,UAAU,CAAC;QAC5B;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;IAC/C;AACF;AAGO,MAAM,SAAS;IACpB,IAAI,2IAAA,CAAA,UAAI,CAAC,UAAU,IAAI;QACrB,2IAAA,CAAA,UAAI,CAAC,MAAM;QACX,OAAO,QAAQ,CAAC,MAAM;IACxB;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,IAAI,CAAC,2IAAA,CAAA,UAAI,CAAC,UAAU,IAAI;YACtB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,8BAA8B;QAC9B,IAAI;YACF,MAAM,QAAQ;YACd,IAAI,OAAO;gBACT,MAAM,sHAAA,CAAA,UAAU,CAAC,IAAI,CAAC,MAAM;gBAC5B,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,UAAU;YACjB,QAAQ,IAAI,CAAC,wDAAwD;QACrE,uBAAuB;QACzB;QAEA,mBAAmB;QACnB;QACA;QAEA,0CAA0C;QAC1C,2IAAA,CAAA,UAAI,CAAC,MAAM;QAEX,wBAAwB;QACxB,WAAW;YACT,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,GAAG;IAEL,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,gBAAgB;QAChB,OAAO,QAAQ,CAAC,MAAM;IACxB;AACF;AAGO,MAAM,YAAY;IACvB,2IAAA,CAAA,UAAI,CAAC,WAAW;AAClB;AAGO,MAAM,aAAa;IACxB,uCAAmC;;IAAW;IAC9C,IAAI,CAAC,2IAAA,CAAA,UAAI,CAAC,UAAU,IAAI;QACtB,OAAO;IACT;IACA,OAAO,2IAAA,CAAA,UAAI,CAAC,UAAU,MAAM;AAC9B;AAGO,MAAM,iBAAiB;IAC5B,uCAAmC;;IAAW;IAE9C,OAAO;IACP,IAAI,aAAa;QACf,OAAO,8HAAA,CAAA,cAAW,CAAC,cAAc;IACnC;IAEA,IAAI,CAAC,2IAAA,CAAA,UAAI,CAAC,UAAU,IAAI;QACtB,OAAO;IACT;IACA,OAAO,2IAAA,CAAA,UAAI,CAAC,cAAc,MAAM;AAClC;AAGO,MAAM,mBAAmB,CAAC;IAC/B,2IAAA,CAAA,UAAI,CAAC,UAAU,CAAC;QACd;QACA,UAAU;IACZ;AACF;AAGO,MAAM,eAAe,OAAO;IACjC,IAAI,CAAC,2IAAA,CAAA,UAAI,CAAC,UAAU,IAAI;QACtB,QAAQ,KAAK,CAAC;QACd;IACF;IAEA,IAAI;QACF,MAAM,2IAAA,CAAA,UAAI,CAAC,iBAAiB,CAAC;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAGO,MAAM,eAAe,OAAO;IACjC,uCAAmC;;IAGnC;IAEA,OAAO;IACP,IAAI,aAAa;QACf,QAAQ,GAAG,CAAC,6CAA6C;QACzD;IACF;IAEA,IAAI,CAAC,2IAAA,CAAA,UAAI,CAAC,UAAU,IAAI;QACtB,QAAQ,KAAK,CAAC;QACd;IACF;IAEA,IAAI;QACF,MAAM,2IAAA,CAAA,UAAI,CAAC,YAAY,CAAC;QACxB,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/0_project/augment-projects/cocodrink-line/client/src/providers/LiffProvider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useEffect, useState } from 'react';\r\nimport liff from '@line/liff';\r\nimport { initializeLiff, isLoggedIn, getUserProfile } from '@/utils/liff';\r\n\r\n// LIFF 上下文類型\r\ninterface LiffContextType {\r\n  isInitialized: boolean;\r\n  isLoggedIn: boolean;\r\n  isInClient: boolean;\r\n  userProfile: any | null;\r\n  error: Error | null;\r\n  isDevelopmentMode?: boolean; // 新增開發模式標記\r\n  pendingRoute?: string | null; // 待處理的路由\r\n}\r\n\r\n// 創建 LIFF 上下文\r\nconst LiffContext = createContext<LiffContextType>({\r\n  isInitialized: false,\r\n  isLoggedIn: false,\r\n  isInClient: false,\r\n  userProfile: null,\r\n  error: null,\r\n  isDevelopmentMode: false,\r\n  pendingRoute: null,\r\n});\r\n\r\n// 開發模式的模擬用戶資料\r\nconst mockUserProfile = {\r\n  userId: 'Udf723d52cde2495367205e5751fb8c8d',\r\n  displayName: 'Andy瑄',\r\n  pictureUrl: 'https://profile.line-scdn.net/0hhnplyM1hN0FdFR30vaZJPi1FNCt-ZG5TIyRxIjgdaSI3IyMVI3svdGwRYCQzJ3AUI3V_LzoXa3FRBkAnQ0PLdVolanBhI3UQcHV_pw',\r\n};\r\n\r\n// LIFF 提供者組件\r\nexport const LiffProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\r\n  // 判斷是否為開發環境\r\n  const isDevelopment = process.env.NODE_ENV === 'development';\r\n  const skipLiffLogin = isDevelopment && process.env.NEXT_PUBLIC_SKIP_LIFF_LOGIN === 'true';\r\n\r\n  const [isInitialized, setIsInitialized] = useState(skipLiffLogin);\r\n  const [isInClient, setIsInClient] = useState(false);\r\n  const [userProfile, setUserProfile] = useState<any | null>(skipLiffLogin ? mockUserProfile : null);\r\n  const [error, setError] = useState<Error | null>(null);\r\n  const [pendingRoute, setPendingRoute] = useState<string | null>(null);\r\n\r\n  console.log('LiffProvider: isDevelopment =', isDevelopment);\r\n  console.log('LiffProvider: NEXT_PUBLIC_SKIP_LIFF_LOGIN =', process.env.NEXT_PUBLIC_SKIP_LIFF_LOGIN);\r\n  console.log('LiffProvider: skipLiffLogin =', skipLiffLogin);\r\n  console.log('LiffProvider: isInitialized =', isInitialized);\r\n\r\n  useEffect(() => {\r\n    // 如果是開發模式且想要繞過 LIFF 登入，直接返回\r\n    if (skipLiffLogin) {\r\n      console.log('開發模式：已繞過 LIFF 登入');\r\n      setIsInitialized(true);\r\n      setUserProfile(mockUserProfile);\r\n      return;\r\n    }\r\n\r\n    // 確保只在客戶端執行 LIFF 初始化\r\n    if (typeof window !== 'undefined') {\r\n      // 初始化 LIFF\r\n      const initLiff = async () => {\r\n        try {\r\n          await initializeLiff();\r\n          setIsInitialized(true);\r\n\r\n          // 檢查是否在 LINE 應用內\r\n          const inClient = liff.isInClient();\r\n          console.log(\"inClient\", inClient);\r\n          setIsInClient(inClient);\r\n          console.log(\"isLoggedIn\", liff.isLoggedIn());\r\n          // 如果用戶已登入，獲取用戶資料\r\n          if (liff.isLoggedIn()) {\r\n            const profile = await getUserProfile();\r\n            console.log(\"getUserProfile profile\", profile);\r\n            setUserProfile(profile);\r\n\r\n            // 檢查是否有 LIFF 狀態參數需要處理\r\n            const urlParams = new URLSearchParams(window.location.search);\r\n            const liffState = urlParams.get('liff.state');\r\n            if (liffState) {\r\n              console.log('LiffProvider: 發現 LIFF 狀態參數:', liffState);\r\n              // URL 解碼處理\r\n              const decodedRoute = decodeURIComponent(liffState);\r\n              console.log('LiffProvider: 解碼後的路由:', decodedRoute);\r\n              setPendingRoute(decodedRoute);\r\n\r\n              // 清除 URL 中的 liff.state 參數\r\n              const newUrl = window.location.pathname;\r\n              window.history.replaceState({}, '', newUrl);\r\n            }\r\n          }\r\n        } catch (err) {\r\n          console.error('LIFF initialization failed', err);\r\n          setError(err instanceof Error ? err : new Error('Failed to initialize LIFF'));\r\n        }\r\n      };\r\n\r\n      initLiff();\r\n    }\r\n  }, [skipLiffLogin]);\r\n\r\n  // 提供 LIFF 上下文\r\n  const value = {\r\n    isInitialized,\r\n    isLoggedIn: skipLiffLogin\r\n      ? true\r\n      : (typeof window !== 'undefined' ? isLoggedIn() : false),\r\n    isInClient,\r\n    userProfile,\r\n    error,\r\n    isDevelopmentMode: skipLiffLogin,\r\n    pendingRoute,\r\n  };\r\n\r\n  console.log('LiffProvider value:', value);\r\n\r\n  return <LiffContext.Provider value={value}>{children}</LiffContext.Provider>;\r\n};\r\n\r\n// 使用 LIFF 上下文的 Hook\r\nexport const useLiff = () => useContext(LiffContext);\r\n\r\nexport default LiffProvider;\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;AAsCwB;;AApCxB;AACA;AACA;;;AAJA;;;;AAiBA,cAAc;AACd,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmB;IACjD,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,OAAO;IACP,mBAAmB;IACnB,cAAc;AAChB;AAEA,cAAc;AACd,MAAM,kBAAkB;IACtB,QAAQ;IACR,aAAa;IACb,YAAY;AACd;AAGO,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;;IAChF,YAAY;IACZ,MAAM,gBAAgB,oDAAyB;IAC/C,MAAM,gBAAgB,iBAAiB,6CAA4C;IAEnF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,uCAAgB;IAC3E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,QAAQ,GAAG,CAAC,iCAAiC;IAC7C,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,iCAAiC;IAC7C,QAAQ,GAAG,CAAC,iCAAiC;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,4BAA4B;YAC5B,wCAAmB;gBACjB,QAAQ,GAAG,CAAC;gBACZ,iBAAiB;gBACjB,eAAe;gBACf;YACF;;QA4CF;iCAAG;QAAC;KAAc;IAElB,cAAc;IACd,MAAM,QAAQ;QACZ;QACA,YAAY,uCACR;QAEJ;QACA;QACA;QACA,mBAAmB;QACnB;IACF;IAEA,QAAQ,GAAG,CAAC,uBAAuB;IAEnC,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GArFa;KAAA;AAwFN,MAAM,UAAU;;IAAM,OAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AAAW;IAAtC;uCAEE", "debugId": null}}, {"offset": {"line": 896, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/0_project/augment-projects/cocodrink-line/client/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useLiff } from '@/providers/LiffProvider';\r\nimport apiService, { tokenManager } from '@/utils/api';\r\nimport { ensureBackendLogin } from '@/utils/liff';\r\n\r\n// 會員狀態類型\r\nexport interface UserStatus {\r\n  isRegistered: boolean;\r\n  isPhoneVerified: boolean;\r\n  isLineVerified: boolean;\r\n  isFullyVerified: boolean;\r\n  userInfo: any | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\n// 會員狀態檢查 Hook\r\nexport const useAuth = () => {\r\n  const isDevelopment = process.env.NODE_ENV === 'development';\r\n  const { isInitialized, isLoggedIn, userProfile } = useLiff();\r\n  const [userStatus, setUserStatus] = useState<UserStatus>({\r\n    isRegistered: false,\r\n    isPhoneVerified: false,\r\n    isLineVerified: false,\r\n    isFullyVerified: false,\r\n    userInfo: null,\r\n    loading: true,\r\n    error: null,\r\n  });\r\n\r\n  // 檢查會員狀態\r\n  const checkUserStatus = async () => {\r\n    // 如果 LIFF 還沒初始化完成，保持 loading 狀態\r\n    if (!isInitialized) {\r\n      setUserStatus(prev => ({\r\n        ...prev,\r\n        loading: true,\r\n      }));\r\n      return;\r\n    }\r\n\r\n    // 如果用戶未登入或沒有用戶資料\r\n    if (!isLoggedIn || !userProfile) {\r\n      setUserStatus(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        isRegistered: false,\r\n        isPhoneVerified: false,\r\n        isLineVerified: false,\r\n        isFullyVerified: false,\r\n        userInfo: null,\r\n      }));\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setUserStatus(prev => ({ ...prev, loading: true, error: null }));\r\n\r\n      // 確保有 JWT token\r\n      if (!tokenManager.hasValidToken()) {\r\n        console.log('No valid JWT token, performing backend login...');\r\n        const loginSuccess = await ensureBackendLogin();\r\n\r\n        if (!loginSuccess) {\r\n          throw new Error('Failed to obtain JWT token');\r\n        }\r\n      }\r\n\r\n      // 調用API檢查用戶資料\r\n      const userData = await apiService.user.getProfile();\r\n\r\n      if (userData && userData.id) {\r\n        const isRegistered = !!userData.id;\r\n        const isPhoneVerified = !!userData.isVerified; // 手機驗證狀態\r\n        const isLineVerified = !!userData.lineVerified; // LINE 驗證狀態\r\n        const isFullyVerified = isRegistered && (isPhoneVerified || isLineVerified); // 任一驗證通過即可\r\n\r\n        setUserStatus({\r\n          isRegistered,\r\n          isPhoneVerified,\r\n          isLineVerified,\r\n          isFullyVerified,\r\n          userInfo: userData,\r\n          loading: false,\r\n          error: null,\r\n        });\r\n      } else {\r\n        // 用戶未註冊\r\n        setUserStatus({\r\n          isRegistered: false,\r\n          isPhoneVerified: false,\r\n          isLineVerified: false,\r\n          isFullyVerified: false,\r\n          userInfo: null,\r\n          loading: false,\r\n          error: null,\r\n        });\r\n      }\r\n    } catch (error: any) {\r\n      console.error('檢查會員狀態失敗:', error);\r\n\r\n      // 如果是404錯誤，表示用戶未註冊\r\n      if (error.response?.status === 404) {\r\n        setUserStatus({\r\n          isRegistered: false,\r\n          isPhoneVerified: false,\r\n          isLineVerified: false,\r\n          isFullyVerified: false,\r\n          userInfo: null,\r\n          loading: false,\r\n          error: null,\r\n        });\r\n      } else {\r\n        setUserStatus(prev => ({\r\n          ...prev,\r\n          loading: false,\r\n          error: error.response?.data?.message || '檢查會員狀態失敗',\r\n        }));\r\n      }\r\n    }\r\n  };\r\n\r\n  // 重新檢查會員狀態\r\n  const refreshUserStatus = () => {\r\n    checkUserStatus();\r\n  };\r\n\r\n  // 開發模式的模擬用戶資料\r\n  const mockUserData = {\r\n    id: '1',\r\n    lineId: 'Udf723d52cde2495367205e5751fb8c8d',\r\n    name: 'Andy瑄',\r\n    phone:  '0912345678',\r\n    isVerified: true,\r\n    createdAt: '2025-05-26 08:35:21',\r\n    updatedAt: '2025-05-26 08:35:43',\r\n  };\r\n\r\n  // 當LIFF初始化完成且用戶登入時檢查狀態\r\n  useEffect(() => {\r\n    console.log('useAuth: 檢查會員狀態');\r\n    console.log('useAuth: isInitialized =', isInitialized);\r\n    console.log('useAuth: isLoggedIn =', isLoggedIn);\r\n    console.log('useAuth: userProfile =', userProfile);\r\n    console.log('useAuth: isDevelopment =', isDevelopment);\r\n    console.log('useAuth: NEXT_PUBLIC_SKIP_LIFF_LOGIN =', process.env.NEXT_PUBLIC_SKIP_LIFF_LOGIN);\r\n\r\n    if (isDevelopment && process.env.NEXT_PUBLIC_SKIP_LIFF_LOGIN === 'true') {\r\n      console.log('開發模式：已繞過 LIFF 登入');\r\n      setUserStatus(prev => ({\r\n        ...prev,\r\n        loading: false,\r\n        isRegistered: true,\r\n        isPhoneVerified: true,\r\n        isLineVerified: true,\r\n        isFullyVerified: true,\r\n        userInfo: mockUserData,\r\n      }));\r\n      //return;\r\n    }\r\n    else if (isInitialized) {\r\n      checkUserStatus();\r\n    }\r\n  }, [isInitialized, isLoggedIn, userProfile]);\r\n\r\n  return {\r\n    ...userStatus,\r\n    refreshUserStatus,\r\n  };\r\n};\r\n\r\nexport default useAuth;\r\n"], "names": [], "mappings": ";;;;AAoBwB;AAlBxB;AACA;AACA;AACA;;AALA;;;;;AAmBO,MAAM,UAAU;;IACrB,MAAM,gBAAgB,oDAAyB;IAC/C,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,cAAc;QACd,iBAAiB;QACjB,gBAAgB;QAChB,iBAAiB;QACjB,UAAU;QACV,SAAS;QACT,OAAO;IACT;IAEA,SAAS;IACT,MAAM,kBAAkB;QACtB,gCAAgC;QAChC,IAAI,CAAC,eAAe;YAClB,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,SAAS;gBACX,CAAC;YACD;QACF;QAEA,iBAAiB;QACjB,IAAI,CAAC,cAAc,CAAC,aAAa;YAC/B,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,SAAS;oBACT,cAAc;oBACd,iBAAiB;oBACjB,gBAAgB;oBAChB,iBAAiB;oBACjB,UAAU;gBACZ,CAAC;YACD;QACF;QAEA,IAAI;YACF,cAAc,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YAE9D,gBAAgB;YAChB,IAAI,CAAC,sHAAA,CAAA,eAAY,CAAC,aAAa,IAAI;gBACjC,QAAQ,GAAG,CAAC;gBACZ,MAAM,eAAe,MAAM,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD;gBAE5C,IAAI,CAAC,cAAc;oBACjB,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,cAAc;YACd,MAAM,WAAW,MAAM,sHAAA,CAAA,UAAU,CAAC,IAAI,CAAC,UAAU;YAEjD,IAAI,YAAY,SAAS,EAAE,EAAE;gBAC3B,MAAM,eAAe,CAAC,CAAC,SAAS,EAAE;gBAClC,MAAM,kBAAkB,CAAC,CAAC,SAAS,UAAU,EAAE,SAAS;gBACxD,MAAM,iBAAiB,CAAC,CAAC,SAAS,YAAY,EAAE,YAAY;gBAC5D,MAAM,kBAAkB,gBAAgB,CAAC,mBAAmB,cAAc,GAAG,WAAW;gBAExF,cAAc;oBACZ;oBACA;oBACA;oBACA;oBACA,UAAU;oBACV,SAAS;oBACT,OAAO;gBACT;YACF,OAAO;gBACL,QAAQ;gBACR,cAAc;oBACZ,cAAc;oBACd,iBAAiB;oBACjB,gBAAgB;oBAChB,iBAAiB;oBACjB,UAAU;oBACV,SAAS;oBACT,OAAO;gBACT;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,aAAa;YAE3B,mBAAmB;YACnB,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,cAAc;oBACZ,cAAc;oBACd,iBAAiB;oBACjB,gBAAgB;oBAChB,iBAAiB;oBACjB,UAAU;oBACV,SAAS;oBACT,OAAO;gBACT;YACF,OAAO;gBACL,cAAc,CAAA,OAAQ,CAAC;wBACrB,GAAG,IAAI;wBACP,SAAS;wBACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW;oBAC1C,CAAC;YACH;QACF;IACF;IAEA,WAAW;IACX,MAAM,oBAAoB;QACxB;IACF;IAEA,cAAc;IACd,MAAM,eAAe;QACnB,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,OAAQ;QACR,YAAY;QACZ,WAAW;QACX,WAAW;IACb;IAEA,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,4BAA4B;YACxC,QAAQ,GAAG,CAAC,yBAAyB;YACrC,QAAQ,GAAG,CAAC,0BAA0B;YACtC,QAAQ,GAAG,CAAC,4BAA4B;YACxC,QAAQ,GAAG,CAAC;YAEZ,wCAAyE;gBACvE,QAAQ,GAAG,CAAC;gBACZ;yCAAc,CAAA,OAAQ,CAAC;4BACrB,GAAG,IAAI;4BACP,SAAS;4BACT,cAAc;4BACd,iBAAiB;4BACjB,gBAAgB;4BAChB,iBAAiB;4BACjB,UAAU;wBACZ,CAAC;;YACD,SAAS;YACX,OACK;;YAEL;QACF;4BAAG;QAAC;QAAe;QAAY;KAAY;IAE3C,OAAO;QACL,GAAG,UAAU;QACb;IACF;AACF;GAxJa;;QAEwC,oIAAA,CAAA,UAAO;;;uCAwJ7C", "debugId": null}}, {"offset": {"line": 1077, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/0_project/augment-projects/cocodrink-line/client/src/utils/liffStateHandler.ts"], "sourcesContent": ["/**\n * LIFF 狀態參數處理工具\n * 專門處理 LINE LIFF 登入後的路由跳轉\n */\n\nexport class LiffStateHandler {\n  private static instance: LiffStateHandler;\n  private pendingRoute: string | null = null;\n  private hasProcessed: boolean = false;\n\n  private constructor() {\n    // 頁面載入時立即檢查 URL 參數\n    this.checkAndStoreLiffState();\n  }\n\n  public static getInstance(): LiffStateHandler {\n    if (!LiffStateHandler.instance) {\n      LiffStateHandler.instance = new LiffStateHandler();\n    }\n    return LiffStateHandler.instance;\n  }\n\n  /**\n   * 檢查並存儲 LIFF 狀態參數\n   */\n  private checkAndStoreLiffState(): void {\n    if (typeof window === 'undefined') return;\n\n    try {\n      const urlParams = new URLSearchParams(window.location.search);\n      console.log('urlParams:', urlParams);// liff.state=%2Fproducts\n      \n      const liffState = urlParams.get('liff.state');\n      \n      if (liffState && liffState.length < 15) {\n        // URL 解碼處理\n        const decodedRoute = decodeURIComponent(liffState);\n        console.log('LiffStateHandler: 發現 LIFF 狀態參數:', decodedRoute);\n        \n        this.pendingRoute = decodedRoute;\n        alert('pendingRoute: ' + decodedRoute );\n        // 立即清除 URL 中的 liff.state 參數\n        const newUrl = window.location.pathname;\n        window.history.replaceState({}, '', newUrl);\n        \n        console.log('LiffStateHandler: 已清除 URL 參數，存儲待處理路由:', decodedRoute);\n      }\n      alert('urlParams:' + urlParams );\n    } catch (error) {\n      console.error('LiffStateHandler: 處理 LIFF 狀態參數失敗:', error);\n    }\n  }\n\n  /**\n   * 獲取待處理的路由\n   */\n  public getPendingRoute(): string | null {\n    return this.pendingRoute;\n  }\n\n  /**\n   * 標記為已處理\n   */\n  public markAsProcessed(): void {\n    this.hasProcessed = true;\n    this.pendingRoute = null;\n  }\n\n  /**\n   * 檢查是否已處理\n   */\n  public isProcessed(): boolean {\n    return this.hasProcessed;\n  }\n\n  /**\n   * 重置狀態\n   */\n  public reset(): void {\n    this.pendingRoute = null;\n    this.hasProcessed = false;\n  }\n\n  /**\n   * 處理路由跳轉\n   */\n  public handleRouting(router: any): boolean {\n    if (this.hasProcessed || !this.pendingRoute) {\n      return false;\n    }\n\n    console.log('LiffStateHandler: 執行路由跳轉到:', this.pendingRoute);\n    router.push(this.pendingRoute);\n    this.markAsProcessed();\n    return true;\n  }\n}\n\n// 導出單例實例\nexport const liffStateHandler = LiffStateHandler.getInstance();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAEM,MAAM;IACX,OAAe,SAA2B;IAClC,eAA8B,KAAK;IACnC,eAAwB,MAAM;IAEtC,aAAsB;QACpB,mBAAmB;QACnB,IAAI,CAAC,sBAAsB;IAC7B;IAEA,OAAc,cAAgC;QAC5C,IAAI,CAAC,iBAAiB,QAAQ,EAAE;YAC9B,iBAAiB,QAAQ,GAAG,IAAI;QAClC;QACA,OAAO,iBAAiB,QAAQ;IAClC;IAEA;;GAEC,GACD,AAAQ,yBAA+B;QACrC,uCAAmC;;QAAM;QAEzC,IAAI;YACF,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;YAC5D,QAAQ,GAAG,CAAC,cAAc,YAAW,yBAAyB;YAE9D,MAAM,YAAY,UAAU,GAAG,CAAC;YAEhC,IAAI,aAAa,UAAU,MAAM,GAAG,IAAI;gBACtC,WAAW;gBACX,MAAM,eAAe,mBAAmB;gBACxC,QAAQ,GAAG,CAAC,mCAAmC;gBAE/C,IAAI,CAAC,YAAY,GAAG;gBACpB,MAAM,mBAAmB;gBACzB,4BAA4B;gBAC5B,MAAM,SAAS,OAAO,QAAQ,CAAC,QAAQ;gBACvC,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI;gBAEpC,QAAQ,GAAG,CAAC,yCAAyC;YACvD;YACA,MAAM,eAAe;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA;;GAEC,GACD,AAAO,kBAAiC;QACtC,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA;;GAEC,GACD,AAAO,kBAAwB;QAC7B,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,YAAY,GAAG;IACtB;IAEA;;GAEC,GACD,AAAO,cAAuB;QAC5B,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA;;GAEC,GACD,AAAO,QAAc;QACnB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,YAAY,GAAG;IACtB;IAEA;;GAEC,GACD,AAAO,cAAc,MAAW,EAAW;QACzC,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YAC3C,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,8BAA8B,IAAI,CAAC,YAAY;QAC3D,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;QAC7B,IAAI,CAAC,eAAe;QACpB,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB,iBAAiB,WAAW", "debugId": null}}, {"offset": {"line": 1168, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/0_project/augment-projects/cocodrink-line/client/src/components/LiffRouter.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { useLiff } from '@/providers/LiffProvider';\nimport { useAuth } from '@/hooks/useAuth';\nimport { liffStateHandler } from '@/utils/liffStateHandler';\n\n/**\n * LIFF 路由處理組件\n * 負責根據用戶的登入和驗證狀態進行路由跳轉\n * \n * 路由邏輯：\n * 1. 未登入 → 保持當前頁面（LIFF 會自動處理登入）\n * 2. 已登入但未完全驗證 → 跳轉到註冊頁面\n * 3. 已登入且完全驗證 → 允許訪問所有頁面\n */\nexport default function LiffRouter() {\n  const router = useRouter();\n  const pathname = usePathname();\n  const { isInitialized, isLoggedIn } = useLiff();\n  const { isFullyVerified, loading: authLoading, isLineVerified, isPhoneVerified, userInfo } = useAuth();\n\n  useEffect(() => {\n    console.log('LiffRouter: 檢查初始化狀態', {\n      isInitialized,\n      authLoading,\n      isLoggedIn,\n      isFullyVerified,\n      currentUrl: typeof window !== 'undefined' ? window.location.href : 'N/A',\n      pendingRoute: liffStateHandler.getPendingRoute()\n    });\n\n    // 等待 LIFF 初始化完成和認證狀態確定\n    if (!isInitialized || authLoading || userInfo === null) {\n      console.log('LiffRouter: 等待初始化完成...');\n      return;\n    }\n\n    // 首先處理 LIFF 狀態參數\n    const hasHandledLiffState = liffStateHandler.handleRouting(router);\n    if (hasHandledLiffState) {\n      console.log('LiffRouter: 已處理 LIFF 狀態跳轉');\n      return;\n    }\n\n    // 檢查當前路徑並處理路由\n    handleRouting();\n  }, [isInitialized, isLoggedIn, isFullyVerified, authLoading, pathname, isLineVerified, isPhoneVerified, userInfo, router]);\n\n  const handleRouting = () => {\n    console.log('LiffRouter: 處理路由', {\n      pathname,\n      isLoggedIn,\n      isFullyVerified,\n      isLineVerified,\n      isPhoneVerified\n    });\n\n    console.log('isFullyVerified:', isFullyVerified);\n    console.log('isLineVerified: ', isLineVerified);\n    console.log('isPhoneVerified: ', isPhoneVerified);\n\n    // 如果用戶未登入，不進行路由跳轉（讓 LIFF 處理登入）\n    if (!isLoggedIn) {\n      console.log('LiffRouter: 用戶未登入，等待 LIFF 處理');\n      return;\n    }\n\n    // 如果用戶已登入但未完全驗證，跳轉到註冊頁面\n    if (isLoggedIn && !isFullyVerified) {\n      if (pathname !== '/register') {\n        console.log('LiffRouter: 用戶未完全驗證，跳轉到註冊頁面');\n        router.push('/register');\n      }\n      return;\n    }\n\n    // 如果用戶已完全驗證，允許訪問所有頁面\n    if (isLoggedIn && isFullyVerified) {\n      console.log('LiffRouter: 用戶已完全驗證，允許訪問當前頁面');\n      \n      // 如果在註冊頁面但已完全驗證，跳轉到首頁\n      if (pathname === '/register') {\n        console.log('LiffRouter: 用戶已驗證但在註冊頁面，跳轉到首頁');\n        router.push('/');\n      }\n    }\n  };\n\n  // 如果還在初始化中，顯示加載指示器\n  if (!isInitialized || authLoading) {\n    return (\n      <div className=\"fixed inset-0 bg-white flex items-center justify-center z-50\">\n        <div className=\"text-center\">\n          <div className=\"w-12 h-12 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">系統初始化中...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // 初始化完成後不渲染任何內容，只處理路由邏輯\n  return null;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAiBe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,UAAO,AAAD;IAC5C,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,cAAc,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEnG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,QAAQ,GAAG,CAAC,uBAAuB;gBACjC;gBACA;gBACA;gBACA;gBACA,YAAY,uCAAgC,OAAO,QAAQ,CAAC,IAAI;gBAChE,cAAc,mIAAA,CAAA,mBAAgB,CAAC,eAAe;YAChD;YAEA,uBAAuB;YACvB,IAAI,CAAC,iBAAiB,eAAe,aAAa,MAAM;gBACtD,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,iBAAiB;YACjB,MAAM,sBAAsB,mIAAA,CAAA,mBAAgB,CAAC,aAAa,CAAC;YAC3D,IAAI,qBAAqB;gBACvB,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,cAAc;YACd;QACF;+BAAG;QAAC;QAAe;QAAY;QAAiB;QAAa;QAAU;QAAgB;QAAiB;QAAU;KAAO;IAEzH,MAAM,gBAAgB;QACpB,QAAQ,GAAG,CAAC,oBAAoB;YAC9B;YACA;YACA;YACA;YACA;QACF;QAEA,QAAQ,GAAG,CAAC,oBAAoB;QAChC,QAAQ,GAAG,CAAC,oBAAoB;QAChC,QAAQ,GAAG,CAAC,qBAAqB;QAEjC,+BAA+B;QAC/B,IAAI,CAAC,YAAY;YACf,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,wBAAwB;QACxB,IAAI,cAAc,CAAC,iBAAiB;YAClC,IAAI,aAAa,aAAa;gBAC5B,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd;YACA;QACF;QAEA,qBAAqB;QACrB,IAAI,cAAc,iBAAiB;YACjC,QAAQ,GAAG,CAAC;YAEZ,sBAAsB;YACtB,IAAI,aAAa,aAAa;gBAC5B,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,mBAAmB;IACnB,IAAI,CAAC,iBAAiB,aAAa;QACjC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,wBAAwB;IACxB,OAAO;AACT;GAvFwB;;QACP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACU,oIAAA,CAAA,UAAO;QACgD,0HAAA,CAAA,UAAO;;;KAJ9E", "debugId": null}}]}