const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const db = require('../models');

class SocketHandler {
  constructor(server) {
    this.io = new Server(server, {
      cors: {
        origin: ["http://localhost:3000", "http://localhost:3001"], // 客戶端和管理端
        methods: ["GET", "POST"],
        credentials: true
      }
    });

    this.connectedClients = new Map(); // 存儲連接的客戶端
    this.connectedAdmins = new Map();  // 存儲連接的管理員
    this.orderTimeouts = new Map();    // 存儲訂單超時計時器

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  // 設置中間件
  setupMiddleware() {
    // JWT 驗證中間件
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token;
        if (!token) {
          return next(new Error('No token provided'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await db.users.findByPk(decoded.id);
        
        if (!user) {
          return next(new Error('User not found'));
        }

        socket.userId = user.id;
        socket.userType = 'client'; // 預設為客戶端
        next();
      } catch (error) {
        next(new Error('Authentication failed'));
      }
    });
  }

  // 設置事件處理器
  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`用戶 ${socket.userId} 已連接`);

      // 客戶端連接
      socket.on('client:join', () => {
        this.connectedClients.set(socket.userId, socket);
        socket.join('clients');
        console.log(`客戶端 ${socket.userId} 加入房間`);
      });

      // 管理員連接
      socket.on('admin:join', () => {
        this.connectedAdmins.set(socket.userId, socket);
        socket.join('admins');
        socket.userType = 'admin';
        console.log(`管理員 ${socket.userId} 加入房間`);
      });

      // 訂單相關事件
      this.setupOrderEvents(socket);

      // 斷線處理
      socket.on('disconnect', () => {
        console.log(`用戶 ${socket.userId} 已斷線`);
        this.connectedClients.delete(socket.userId);
        this.connectedAdmins.delete(socket.userId);
      });
    });
  }

  // 設置訂單相關事件
  setupOrderEvents(socket) {
    // 管理員接受訂單
    socket.on('admin:accept_order', async (data) => {
      try {
        const { orderId } = data;
        
        // 更新訂單狀態
        const order = await db.orders.findByPk(orderId);
        if (!order) {
          socket.emit('error', { message: '訂單不存在' });
          return;
        }

        if (order.status !== 'pending') {
          socket.emit('error', { message: '訂單狀態不正確' });
          return;
        }

        await order.update({ status: 'processing' });

        // 清除超時計時器
        if (this.orderTimeouts.has(orderId)) {
          clearTimeout(this.orderTimeouts.get(orderId));
          this.orderTimeouts.delete(orderId);
        }

        // 通知客戶端訂單已被接受
        const clientSocket = this.connectedClients.get(order.userId);
        if (clientSocket) {
          clientSocket.emit('order:accepted', {
            orderId: order.id,
            orderNumber: order.orderNumber,
            message: '您的訂單已被接受，正在製作中'
          });
        }

        // 通知所有管理員
        this.io.to('admins').emit('order:status_changed', {
          orderId: order.id,
          status: 'processing',
          acceptedBy: socket.userId
        });

        socket.emit('admin:accept_success', { orderId });
        
      } catch (error) {
        console.error('接受訂單失敗:', error);
        socket.emit('error', { message: '接受訂單失敗' });
      }
    });

    // 訂單狀態更新
    socket.on('admin:update_order_status', async (data) => {
      try {
        const { orderId, status } = data;
        
        const order = await db.orders.findByPk(orderId);
        if (!order) {
          socket.emit('error', { message: '訂單不存在' });
          return;
        }

        await order.update({ status });

        // 通知客戶端
        const clientSocket = this.connectedClients.get(order.userId);
        if (clientSocket) {
          clientSocket.emit('order:status_updated', {
            orderId: order.id,
            orderNumber: order.orderNumber,
            status,
            message: this.getStatusMessage(status)
          });
        }

        // 通知所有管理員
        this.io.to('admins').emit('order:status_changed', {
          orderId: order.id,
          status,
          updatedBy: socket.userId
        });

      } catch (error) {
        console.error('更新訂單狀態失敗:', error);
        socket.emit('error', { message: '更新訂單狀態失敗' });
      }
    });
  }

  // 發送新訂單通知給管理員
  async notifyNewOrder(order) {
    try {
      // 獲取完整的訂單資訊
      const fullOrder = await db.orders.findByPk(order.id, {
        include: [
          {
            model: db.orderItems,
            as: 'items',
            include: [
              {
                model: db.products,
                as: 'product'
              }
            ]
          },
          {
            model: db.users,
            as: 'user',
            attributes: ['id', 'name', 'phone']
          }
        ]
      });

      // 通知所有連接的管理員
      this.io.to('admins').emit('admin:new_order', {
        order: fullOrder,
        timestamp: new Date().toISOString()
      });

      // 設置超時處理
      await this.setupOrderTimeout(order.id, order.userId);

      console.log(`新訂單 ${order.orderNumber} 已通知管理員`);
    } catch (error) {
      console.error('發送新訂單通知失敗:', error);
    }
  }

  // 設置訂單超時處理
  async setupOrderTimeout(orderId, userId) {
    try {
      // 獲取超時設定
      const timeoutSetting = await db.systemSettings.findOne({
        where: { key: 'order_wait_timeout' }
      });
      
      const timeoutSeconds = timeoutSetting ? parseInt(timeoutSetting.value) : 60;
      
      // 設置超時計時器
      const timeoutId = setTimeout(async () => {
        try {
          const order = await db.orders.findByPk(orderId);
          
          if (order && order.status === 'pending') {
            // 通知客戶端超時
            const clientSocket = this.connectedClients.get(userId);
            if (clientSocket) {
              clientSocket.emit('order:timeout', {
                orderId: order.id,
                orderNumber: order.orderNumber,
                message: '等待接單超時，請選擇重新送單或取消訂單'
              });
            }
            
            console.log(`訂單 ${order.orderNumber} 等待超時`);
          }
          
          this.orderTimeouts.delete(orderId);
        } catch (error) {
          console.error('處理訂單超時失敗:', error);
        }
      }, timeoutSeconds * 1000);

      this.orderTimeouts.set(orderId, timeoutId);
      
    } catch (error) {
      console.error('設置訂單超時失敗:', error);
    }
  }

  // 獲取狀態訊息
  getStatusMessage(status) {
    const messages = {
      'pending': '等待接單中',
      'processing': '製作中',
      'completed': '製作完成，請取餐',
      'cancelled': '訂單已取消'
    };
    return messages[status] || '狀態未知';
  }

  // 獲取 Socket.io 實例
  getIO() {
    return this.io;
  }
}

module.exports = SocketHandler;
