'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useLiff } from '@/providers/LiffProvider';
import { useAuth } from '@/hooks/useAuth';

/**
 * LIFF 路由處理組件
 * 處理從 LINE LIFF URL 的路由跳轉
 *
 * LIFF URL 格式：
 * - https://liff.line.me/**********-vMp0L0WA -> 首頁
 * - https://liff.line.me/**********-vMp0L0WA/products -> 產品頁面
 * - https://liff.line.me/**********-vMp0L0WA/orders -> 訂單頁面
 */
export default function LiffRouter() {
  const router = useRouter();
  const pathname = usePathname();
  const { isInitialized, isLoggedIn, pendingRoute } = useLiff();
  const { isFullyVerified, loading: authLoading, isLineVerified, isPhoneVerified, userInfo } = useAuth();
  const [hasProcessedLiffState, setHasProcessedLiffState] = useState(false);

  // 處理 LIFF 狀態參數
  const handleLiffState = () => {
    if (hasProcessedLiffState) return;

    try {
      // 檢查 URL 中是否有 liff.state 參數
      const urlParams = new URLSearchParams(window.location.search);
      const liffState = urlParams.get('liff.state');

      console.log('LiffRouter: 檢查 LIFF 狀態參數', { liffState, currentPath: pathname });

      if (liffState) {
        console.log('LiffRouter: 發現 LIFF 狀態參數，準備跳轉到:', liffState);

        // 清除 URL 中的 liff.state 參數
        const newUrl = window.location.pathname;
        window.history.replaceState({}, '', newUrl);

        // 跳轉到目標路由
        router.push(liffState);
        setHasProcessedLiffState(true);
        return true;
      }
    } catch (error) {
      console.error('處理 LIFF 狀態參數失敗:', error);
    }

    setHasProcessedLiffState(true);
    return false;
  };

  useEffect(() => {
    console.log('LiffRouter: 檢查初始化狀態', {
      isInitialized,
      authLoading,
      isLoggedIn,
      isFullyVerified,
      userInfo
    });

    // 等待 LIFF 初始化完成和認證狀態確定
    if (!isInitialized || userInfo == null || authLoading) {
      console.log('LiffRouter: 等待初始化完成...');
      return;
    }

    // 首先處理來自 LiffProvider 的 pendingRoute
    if (pendingRoute && !hasProcessedLiffState) {
      console.log('LiffRouter: 處理來自 LiffProvider 的 pendingRoute:', pendingRoute);
      router.push(pendingRoute);
      setHasProcessedLiffState(true);
      return;
    }

    // 然後處理 URL 中的 LIFF 狀態參數（備用方案）
    const hasLiffState = handleLiffState();

    // 如果有 LIFF 狀態參數，等待跳轉完成
    if (hasLiffState) {
      return;
    }

    // 檢查當前路徑並處理路由
    handleRouting();
  }, [isInitialized, isLoggedIn, isFullyVerified, authLoading, pathname, isLineVerified, isPhoneVerified, userInfo]);

  const handleRouting = () => {
    console.log('LiffRouter: 處理路由', {
      pathname,
      isLoggedIn,
      isFullyVerified,
      isLineVerified,
      isPhoneVerified,
      userInfo
    });

    // 如果用戶未登入，且不在首頁，跳轉到首頁進行登入
    if (!isLoggedIn && pathname !== '/') {
      console.log('LiffRouter: 用戶未登入，跳轉到首頁');
      router.push('/');
      return;
    }

    // 如果用戶已登入但未完全驗證，且要訪問需要驗證的頁面
    if (isLoggedIn && !isFullyVerified) {
      console.log('isFullyVerified:', isFullyVerified);
      console.log('isLineVerified: ', isLineVerified);
      console.log('isPhoneVerified: ', isPhoneVerified);
      const protectedRoutes = ['/products', '/orders', '/cart'];

      if (protectedRoutes.includes(pathname)) {
        console.log('LiffRouter: 用戶未完全驗證，跳轉到註冊頁面');
        router.push('/register');
        return;
      }
    }

    // 處理特定路由的跳轉邏輯
    switch (pathname) {
      case '/products':
        // 產品頁面：需要登入和驗證
        if (isLoggedIn && isFullyVerified) {
          console.log('LiffRouter: 允許訪問產品頁面');
        } else if (isLoggedIn && !isFullyVerified) {
          console.log('LiffRouter: 需要驗證，跳轉到註冊頁面');
          router.push('/register');
        } else {
          console.log('LiffRouter: 需要登入，跳轉到首頁');
          router.push('/');
        }
        break;

      case '/orders':
        // 訂單頁面：需要登入和驗證
        if (isLoggedIn && isFullyVerified) {
          console.log('LiffRouter: 允許訪問訂單頁面');
        } else if (isLoggedIn && !isFullyVerified) {
          console.log('LiffRouter: 需要驗證，跳轉到註冊頁面');
          router.push('/register');
        } else {
          console.log('LiffRouter: 需要登入，跳轉到首頁');
          router.push('/');
        }
        break;

      case '/cart':
        // 購物車頁面：需要登入和驗證
        if (isLoggedIn && isFullyVerified) {
          console.log('LiffRouter: 允許訪問購物車頁面');
        } else if (isLoggedIn && !isFullyVerified) {
          console.log('LiffRouter: 需要驗證，跳轉到註冊頁面');
          router.push('/register');
        } else {
          console.log('LiffRouter: 需要登入，跳轉到首頁');
          router.push('/');
        }
        break;

      case '/register':
        // 註冊頁面：需要登入
        if (!isLoggedIn) {
          console.log('LiffRouter: 註冊頁面需要登入，跳轉到首頁');
          router.push('/');
        }
        break;

      case '/':
        // 首頁：無特殊限制
        console.log('LiffRouter: 在首頁，無需跳轉');
        break;

      default:
        // 其他頁面：檢查是否存在
        console.log('LiffRouter: 未知路由', pathname);
        break;
    }
  };

  // 如果還在初始化中，顯示加載指示器
  if (!isInitialized || authLoading) {
    return (
      <div className="fixed inset-0 bg-white flex items-center justify-center z-50">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">系統初始化中...</p>
        </div>
      </div>
    );
  }

  // 初始化完成後不渲染任何內容，只處理路由邏輯
  return null;
}
