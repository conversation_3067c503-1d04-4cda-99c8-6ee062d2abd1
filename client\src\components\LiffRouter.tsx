'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useLiff } from '@/providers/LiffProvider';
import { useAuth } from '@/hooks/useAuth';
import { urlStateHandler } from '@/utils/urlStateHandler';

/**
 * LIFF 路由處理組件
 * 負責根據用戶的登入和驗證狀態進行路由跳轉
 * 
 * 路由邏輯：
 * 1. 未登入 → 保持當前頁面（LIFF 會自動處理登入）
 * 2. 已登入但未完全驗證 → 跳轉到註冊頁面
 * 3. 已登入且完全驗證 → 允許訪問所有頁面
 */
export default function LiffRouter() {
  const router = useRouter();
  const pathname = usePathname();
  const { isInitialized, isLoggedIn } = useLiff();
  const { isFullyVerified, loading: authLoading, isLineVerified, isPhoneVerified, userInfo } = useAuth();

  useEffect(() => {
    console.log('LiffRouter: 檢查初始化狀態', {
      isInitialized,
      authLoading,
      isLoggedIn,
      isFullyVerified,
      currentUrl: typeof window !== 'undefined' ? window.location.href : 'N/A',
      pendingRoute: urlStateHandler.getPendingRoute()
    });

    // 如果用戶未登入，保存當前路徑
    if (isInitialized && !isLoggedIn) {
      urlStateHandler.saveCurrentRoute();
      return;
    }

    // 等待 LIFF 初始化完成和認證狀態確定
    if (!isInitialized || authLoading || userInfo === null) {
      console.log('LiffRouter: 等待初始化完成...');
      return;
    }

    // 如果用戶已登入，處理待處理的路由
    if (isLoggedIn) {
      const hasHandledRoute = urlStateHandler.handleRouting(router);
      if (hasHandledRoute) {
        console.log('LiffRouter: 已處理待處理路由跳轉');
        return;
      }
    }

    // 檢查當前路徑並處理路由
    handleRouting();
  }, [isInitialized, isLoggedIn, isFullyVerified, authLoading, pathname, isLineVerified, isPhoneVerified, userInfo, router]);

  const handleRouting = () => {
    console.log('LiffRouter: 處理路由', {
      pathname,
      isLoggedIn,
      isFullyVerified,
      isLineVerified,
      isPhoneVerified
    });

    console.log('isFullyVerified:', isFullyVerified);
    console.log('isLineVerified: ', isLineVerified);
    console.log('isPhoneVerified: ', isPhoneVerified);

    // 如果用戶未登入，不進行路由跳轉（讓 LIFF 處理登入）
    if (!isLoggedIn) {
      console.log('LiffRouter: 用戶未登入，等待 LIFF 處理');
      return;
    }

    // 如果用戶已登入但未完全驗證，跳轉到註冊頁面
    if (isLoggedIn && !isFullyVerified) {
      if (pathname !== '/register') {
        console.log('LiffRouter: 用戶未完全驗證，跳轉到註冊頁面');
        router.push('/register');
      }
      return;
    }

    // 如果用戶已完全驗證，允許訪問所有頁面
    if (isLoggedIn && isFullyVerified) {
      console.log('LiffRouter: 用戶已完全驗證，允許訪問當前頁面');
      
      // 如果在註冊頁面但已完全驗證，跳轉到首頁
      if (pathname === '/register') {
        console.log('LiffRouter: 用戶已驗證但在註冊頁面，跳轉到首頁');
        router.push('/');
      }
    }
  };

  // 如果還在初始化中，顯示加載指示器
  if (!isInitialized || authLoading) {
    return (
      <div className="fixed inset-0 bg-white flex items-center justify-center z-50">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">系統初始化中...</p>
        </div>
      </div>
    );
  }

  // 初始化完成後不渲染任何內容，只處理路由邏輯
  return null;
}

