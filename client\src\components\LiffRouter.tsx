'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useLiff } from '@/providers/LiffProvider';
import { useAuth } from '@/hooks/useAuth';

/**
 * LIFF 路由處理組件
 * 處理從 LINE LIFF URL 的路由跳轉
 * 
 * LIFF URL 格式：
 * - https://liff.line.me/**********-vMp0L0WA -> 首頁
 * - https://liff.line.me/**********-vMp0L0WA/products -> 產品頁面
 * - https://liff.line.me/**********-vMp0L0WA/orders -> 訂單頁面
 */
export default function LiffRouter() {
  const router = useRouter();
  const pathname = usePathname();
  const { isInitialized, isLoggedIn } = useLiff();
  const { isFullyVerified, loading: authLoading } = useAuth();

  useEffect(() => {
    // 等待 LIFF 初始化完成
    if (!isInitialized || authLoading) {
      return;
    }

    // 檢查當前路徑並處理路由
    handleRouting();
  }, [isInitialized, isLoggedIn, isFullyVerified, authLoading, pathname]);

  const handleRouting = () => {
    console.log('LiffRouter: 處理路由', {
      pathname,
      isLoggedIn,
      isFullyVerified
    });

    // 如果用戶未登入，且不在首頁，跳轉到首頁進行登入
    if (!isLoggedIn && pathname !== '/') {
      console.log('LiffRouter: 用戶未登入，跳轉到首頁');
      router.push('/');
      return;
    }

    // 如果用戶已登入但未完全驗證，且要訪問需要驗證的頁面
    if (isLoggedIn && !isFullyVerified) {
      const protectedRoutes = ['/products', '/orders', '/cart'];
      
      if (protectedRoutes.includes(pathname)) {
        console.log('LiffRouter: 用戶未完全驗證，跳轉到註冊頁面');
        router.push('/register');
        return;
      }
    }

    // 處理特定路由的跳轉邏輯
    switch (pathname) {
      case '/products':
        // 產品頁面：需要登入和驗證
        if (isLoggedIn && isFullyVerified) {
          console.log('LiffRouter: 允許訪問產品頁面');
        } else if (isLoggedIn && !isFullyVerified) {
          console.log('LiffRouter: 需要驗證，跳轉到註冊頁面');
          router.push('/register');
        } else {
          console.log('LiffRouter: 需要登入，跳轉到首頁');
          router.push('/');
        }
        break;

      case '/orders':
        // 訂單頁面：需要登入和驗證
        if (isLoggedIn && isFullyVerified) {
          console.log('LiffRouter: 允許訪問訂單頁面');
        } else if (isLoggedIn && !isFullyVerified) {
          console.log('LiffRouter: 需要驗證，跳轉到註冊頁面');
          router.push('/register');
        } else {
          console.log('LiffRouter: 需要登入，跳轉到首頁');
          router.push('/');
        }
        break;

      case '/cart':
        // 購物車頁面：需要登入和驗證
        if (isLoggedIn && isFullyVerified) {
          console.log('LiffRouter: 允許訪問購物車頁面');
        } else if (isLoggedIn && !isFullyVerified) {
          console.log('LiffRouter: 需要驗證，跳轉到註冊頁面');
          router.push('/register');
        } else {
          console.log('LiffRouter: 需要登入，跳轉到首頁');
          router.push('/');
        }
        break;

      case '/register':
        // 註冊頁面：需要登入
        if (!isLoggedIn) {
          console.log('LiffRouter: 註冊頁面需要登入，跳轉到首頁');
          router.push('/');
        }
        break;

      case '/':
        // 首頁：無特殊限制
        console.log('LiffRouter: 在首頁，無需跳轉');
        break;

      default:
        // 其他頁面：檢查是否存在
        console.log('LiffRouter: 未知路由', pathname);
        break;
    }
  };

  // 這個組件不渲染任何內容，只處理路由邏輯
  return null;
}
