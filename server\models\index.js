const dbConfig = require('../config/db.config');
const Sequelize = require('sequelize');

// 創建 Sequelize 實例
const sequelize = new Sequelize(
  dbConfig.DB,
  dbConfig.USER,
  dbConfig.PASSWORD,
  {
    host: dbConfig.HOST,
    port: dbConfig.PORT,
    dialect: dbConfig.dialect,
    operatorsAliases: 0,
    pool: {
      max: dbConfig.pool.max,
      min: dbConfig.pool.min,
      acquire: dbConfig.pool.acquire,
      idle: dbConfig.pool.idle
    },
    logging: process.env.NODE_ENV === 'development' ? console.log : false
  }
);

const db = {};

db.Sequelize = Sequelize;
db.sequelize = sequelize;

// 導入模型
db.users = require('./user.model')(sequelize, Sequelize);
db.userLogs = require('./userLog.model')(sequelize, Sequelize);
db.products = require('./product.model')(sequelize, Sequelize);
db.productOptions = require('./productOption.model')(sequelize, Sequelize);
db.orders = require('./order.model')(sequelize, Sequelize);
db.orderItems = require('./orderItem.model')(sequelize, Sequelize);
db.systemSettings = require('./systemSetting.model')(sequelize, Sequelize);

// 設置關聯
// 用戶與訂單的關聯 (一對多)
db.users.hasMany(db.orders, { as: 'orders' });
db.orders.belongsTo(db.users, {
  foreignKey: 'userId',
  as: 'user'
});

// 用戶與日誌的關聯 (一對多)
db.users.hasMany(db.userLogs, { as: 'logs' });
db.userLogs.belongsTo(db.users, {
  foreignKey: 'userId',
  as: 'user'
});

// 產品與產品選項的關聯 (一對多)
db.products.hasMany(db.productOptions, { as: 'options' });
db.productOptions.belongsTo(db.products, {
  foreignKey: 'productId',
  as: 'product'
});

// 訂單與訂單項目的關聯 (一對多)
db.orders.hasMany(db.orderItems, { as: 'items' });
db.orderItems.belongsTo(db.orders, {
  foreignKey: 'orderId',
  as: 'order'
});

// 產品與訂單項目的關聯 (一對多)
db.products.hasMany(db.orderItems, { as: 'orderItems' });
db.orderItems.belongsTo(db.products, {
  foreignKey: 'productId',
  as: 'product'
});

module.exports = db;
