'use client';

import React, { useState, useEffect } from 'react';
import { useSocket } from '@/hooks/useSocket';
import apiService from '@/utils/api';

interface OrderWaitingProps {
  order: {
    id: number;
    orderNumber: string;
    totalPrice: number;
  };
  onOrderAccepted: () => void;
  onOrderCancelled: () => void;
}

const OrderWaiting: React.FC<OrderWaitingProps> = ({
  order,
  onOrderAccepted,
  onOrderCancelled
}) => {
  const { connect, onOrderAccepted: onSocketOrderAccepted, onOrderTimeout } = useSocket();
  const [timeLeft, setTimeLeft] = useState(60); // 預設 60 秒
  const [isTimeout, setIsTimeout] = useState(false);
  const [isResubmitting, setIsResubmitting] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);

  useEffect(() => {
    // 連接 Socket
    connect();

    // 監聽訂單被接受
    onSocketOrderAccepted((data) => {
      console.log('訂單已被接受:', data);
      onOrderAccepted();
    });

    // 監聽訂單超時
    onOrderTimeout((data) => {
      console.log('訂單等待超時:', data);
      setIsTimeout(true);
    });

    // 獲取系統設定的等待時間
    const fetchWaitTime = async () => {
      try {
        const response = await apiService.get('/api/settings/order_wait_timeout');
        if (response.data && response.data.value) {
          setTimeLeft(response.data.value);
        }
      } catch (error) {
        console.error('獲取等待時間設定失敗:', error);
      }
    };

    fetchWaitTime();
  }, [connect, onSocketOrderAccepted, onOrderTimeout, onOrderAccepted]);

  useEffect(() => {
    if (isTimeout) return;

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          setIsTimeout(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isTimeout]);

  const handleResubmit = async () => {
    setIsResubmitting(true);
    try {
      await apiService.put(`/api/orders/${order.id}/resubmit`);
      setIsTimeout(false);
      setTimeLeft(60); // 重置計時器
    } catch (error) {
      console.error('重新送單失敗:', error);
      alert('重新送單失敗，請稍後再試');
    } finally {
      setIsResubmitting(false);
    }
  };

  const handleCancel = async () => {
    setIsCancelling(true);
    try {
      await apiService.put(`/api/orders/${order.id}/cancel`);
      onOrderCancelled();
    } catch (error) {
      console.error('取消訂單失敗:', error);
      alert('取消訂單失敗，請稍後再試');
    } finally {
      setIsCancelling(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="text-center">
          {!isTimeout ? (
            <>
              <div className="mb-4">
                <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  等待店家接單中...
                </h3>
                <p className="text-gray-600 mb-4">
                  訂單編號：{order.orderNumber}
                </p>
                <p className="text-gray-600 mb-4">
                  總金額：NT${order.totalPrice}
                </p>
              </div>
              
              <div className="mb-6">
                <div className="text-2xl font-bold text-orange-500 mb-2">
                  {formatTime(timeLeft)}
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-orange-500 h-2 rounded-full transition-all duration-1000"
                    style={{ width: `${(timeLeft / 60) * 100}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  剩餘等待時間
                </p>
              </div>
            </>
          ) : (
            <>
              <div className="mb-4">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  等待接單超時
                </h3>
                <p className="text-gray-600 mb-4">
                  很抱歉，店家可能正在忙碌中
                </p>
                <p className="text-gray-600 mb-6">
                  您可以選擇重新送單或取消訂單
                </p>
              </div>
            </>
          )}

          {isTimeout && (
            <div className="flex space-x-3">
              <button
                onClick={handleResubmit}
                disabled={isResubmitting}
                className="flex-1 bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isResubmitting ? '送單中...' : '重新送單'}
              </button>
              <button
                onClick={handleCancel}
                disabled={isCancelling}
                className="flex-1 bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isCancelling ? '取消中...' : '取消訂單'}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderWaiting;
