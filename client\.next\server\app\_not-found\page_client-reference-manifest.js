globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"894":{"*":{"id":"6346","name":"*","chunks":[],"async":false}},"1074":{"*":{"id":"9626","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"7173","name":"*","chunks":[],"async":false}},"4222":{"*":{"id":"584","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"8827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"7924","name":"*","chunks":[],"async":false}},"5187":{"*":{"id":"7679","name":"*","chunks":[],"async":false}},"5362":{"*":{"id":"7614","name":"*","chunks":[],"async":false}},"6014":{"*":{"id":"1855","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"5656","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"99","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"8243","name":"*","chunks":[],"async":false}},"8665":{"*":{"id":"7403","name":"*","chunks":[],"async":false}},"8679":{"*":{"id":"4319","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"2763","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":2093,"name":"*","chunks":["932","static/chunks/932-4e4bfa8a9b82d390.js","222","static/chunks/222-e25bee06947ca694.js","177","static/chunks/app/layout-f3795f6b01cac877.js"],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":7735,"name":"*","chunks":["932","static/chunks/932-4e4bfa8a9b82d390.js","222","static/chunks/222-e25bee06947ca694.js","177","static/chunks/app/layout-f3795f6b01cac877.js"],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\globals.css":{"id":347,"name":"*","chunks":["932","static/chunks/932-4e4bfa8a9b82d390.js","222","static/chunks/222-e25bee06947ca694.js","177","static/chunks/app/layout-f3795f6b01cac877.js"],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\components\\LiffRouter.tsx":{"id":5362,"name":"*","chunks":["932","static/chunks/932-4e4bfa8a9b82d390.js","222","static/chunks/222-e25bee06947ca694.js","177","static/chunks/app/layout-f3795f6b01cac877.js"],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\providers\\LiffProvider.tsx":{"id":4222,"name":"*","chunks":["932","static/chunks/932-4e4bfa8a9b82d390.js","222","static/chunks/222-e25bee06947ca694.js","177","static/chunks/app/layout-f3795f6b01cac877.js"],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\cart\\page.tsx":{"id":1074,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\products\\page.tsx":{"id":8679,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\register\\page.tsx":{"id":6014,"name":"*","chunks":[],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\page.tsx":{"id":8665,"name":"*","chunks":["932","static/chunks/932-4e4bfa8a9b82d390.js","659","static/chunks/659-131dc016b8b4f144.js","685","static/chunks/685-e228c1cbf3fa1733.js","222","static/chunks/222-e25bee06947ca694.js","974","static/chunks/app/page-e20396e7cc0aecdb.js"],"async":false},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\orders\\page.tsx":{"id":5187,"name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\":[],"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\layout":[{"inlined":false,"path":"static/css/08b248d050046b2b.css"}],"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\page":[],"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\_not-found\\page":[]},"rscModuleMapping":{"347":{"*":{"id":"1135","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"6444","name":"*","chunks":[],"async":false}},"1074":{"*":{"id":"905","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"1307","name":"*","chunks":[],"async":false}},"4222":{"*":{"id":"1358","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"2089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"6042","name":"*","chunks":[],"async":false}},"5187":{"*":{"id":"4346","name":"*","chunks":[],"async":false}},"5362":{"*":{"id":"5096","name":"*","chunks":[],"async":false}},"6014":{"*":{"id":"4530","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"8170","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"9477","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"9345","name":"*","chunks":[],"async":false}},"8665":{"*":{"id":"1204","name":"*","chunks":[],"async":false}},"8679":{"*":{"id":"8547","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"6577","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}