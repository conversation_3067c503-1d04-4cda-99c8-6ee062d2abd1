import liff from '@line/liff';
import apiService, { tokenManager } from './api';
import { liffDevMock, devLoginWithBackend } from './liff-dev';

// LIFF ID
const LIFF_ID = typeof window !== 'undefined'
  ? process.env.NEXT_PUBLIC_LIFF_ID || '2007460761-vMp0L0WA'  // 客戶端
  : '2007460761-vMp0L0WA';  // 伺服器端

// 檢查是否為開發模式
const isDevMode = () => {
  return process.env.NODE_ENV === 'development' &&
         typeof window !== 'undefined' &&
         window.location.hostname === 'localhost';
};

// LIFF 初始化
export const initializeLiff = async (): Promise<void> => {
  try {
    // 開發模式：使用模擬器
    if (isDevMode()) {
      console.log('🔧 Development mode: Using LIFF mock');
      await liffDevMock.init({ liffId: LIFF_ID });
      return;
    }

    if (!LIFF_ID) {
      throw new Error('LIFF ID is not defined');
    }

    await liff.init({
      liffId: LIFF_ID,
      withLoginOnExternalBrowser: true, // 在外部瀏覽器中自動登入
    });
    console.log('LIFF initialization succeeded');
  } catch (error) {
    console.error('LIFF initialization failed', error);
    throw error;
  }
};

// 檢查是否在 LIFF 環境中
export const isInLiffBrowser = (): boolean => {
  if (typeof window === 'undefined') return false;

  // 開發模式
  if (isDevMode()) {
    return liffDevMock.isInClient();
  }

  return liff.isInClient();
};

// 檢查用戶是否已登入
export const isLoggedIn = (): boolean => {
  if (typeof window === 'undefined') return false;

  // 開發模式
  if (isDevMode()) {
    return liffDevMock.isLoggedIn();
  }

  try {
    // 確保 LIFF 已初始化
    if (!liff) {
      console.warn('LIFF is not initialized yet');
      return false;
    }
    return liff.isLoggedIn();
  } catch (error) {
    console.error('Error checking login status', error);
    return false;
  }
};

// 獲取用戶資料
export const getUserProfile = async () => {
  try {
    // 開發模式
    if (isDevMode()) {
      if (!liffDevMock.isLoggedIn()) {
        throw new Error('User is not logged in');
      }
      const profile = await liffDevMock.getProfile();
      console.log('profile', profile);
      return {
        userId: profile.userId,
        displayName: profile.displayName,
        pictureUrl: profile.pictureUrl,
        email: "<EMAIL>",
      };
    }

    if (!liff.isLoggedIn()) {
      throw new Error('User is not logged in');
    }

    // 獲取用戶資料
    const profile = await liff.getProfile();
    console.log("profile:", profile);
    // 取得使用者 email
    // 後台的 Email address permission 要是「Applied」
    // LIFF 的設定，Scopes 的「email*」要打勾
    // 使用者在登入時，「電子郵件帳號」也要是「許可」的
    var user = liff.getDecodedIDToken();
    console.log("user:", user);
    var email = user?.email;
    console.log("email:", email);
    return {
      userId: profile.userId,
      displayName: profile.displayName,
      pictureUrl: profile.pictureUrl,
      email: email,
    };
  } catch (error) {
    console.error('Failed to get user profile', error);
    throw error;
  }
};

// LINE 登入
export const login = (): void => {
  if (!liff.isLoggedIn()) {
    liff.login();
  }
};

// 完整的 LINE 登入流程（包含後端 API 調用）
export const loginWithBackend = async (userIndex = 0) => {
  try {
    // 開發模式：使用專用的開發登入流程
    if (isDevMode()) {
      return await devLoginWithBackend(userIndex);
    }

    if (!liff.isLoggedIn()) {
      throw new Error('User is not logged in to LINE');
    }

    // 取得 access token
    const accessToken = getAccessToken();
    if (!accessToken) {
      throw new Error('Unable to get access token');
    }

    // 調用後端 API 進行登入
    const response = await apiService.auth.lineLogin(accessToken);

    console.log('Backend login successful:', response);
    return response;
  } catch (error) {
    console.error('Backend login failed:', error);
    throw error;
  }
};

// 檢查並自動執行後端登入（如果需要）
export const ensureBackendLogin = async () => {
  try {
    // 檢查是否有有效的 JWT token
    if (tokenManager.hasValidToken()) {
      console.log('Valid JWT token found, no need to login');
      return true;
    }

    // 檢查 LIFF 是否已登入
    if (!isLoggedIn()) {
      console.log('User not logged in to LIFF');
      return false;
    }

    console.log('LIFF logged in but no JWT token, performing backend login...');

    // 執行後端登入
    await loginWithBackend();

    console.log('Backend login completed successfully');
    return true;
  } catch (error) {
    console.error('Auto backend login failed:', error);
    return false;
  }
};

// 清理用戶相關的本地資料
export const clearUserData = (): void => {
  try {
    // 清理 localStorage 中的用戶相關資料
    const keysToRemove = [
      'userProfile',
      'userPreferences',
      'cartItems',
      'lastOrderId',
      'userSettings'
    ];

    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });

    // 清理 sessionStorage
    sessionStorage.clear();

    console.log('User data cleared successfully');
  } catch (error) {
    console.error('Error clearing user data:', error);
  }
};

// 清理所有認證相關的 token 和資料
export const clearAuthTokens = (): void => {
  try {
    // 清理可能儲存在 localStorage 中的 token
    const tokenKeys = [
      'access_token',
      'refresh_token',
      'id_token',
      'liff_token',
      'auth_token'
    ];

    tokenKeys.forEach(key => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });

    console.log('Auth tokens cleared successfully');
  } catch (error) {
    console.error('Error clearing auth tokens:', error);
  }
};

// LINE 登出（基本版本，保持向後兼容）
export const logout = (): void => {
  if (liff.isLoggedIn()) {
    liff.logout();
    window.location.reload();
  }
};

// LINE 登出（完整版本，包含資料清理和 API 調用）
export const logoutWithCleanup = async (): Promise<void> => {
  try {
    if (!liff.isLoggedIn()) {
      console.warn('User is not logged in');
      return;
    }

    // 1. 先調用伺服器端登出 API（如果有 token）
    try {
      const token = getAccessToken();
      if (token) {
        await apiService.auth.logout();
        console.log('Server logout successful');
      }
    } catch (apiError) {
      console.warn('Server logout failed, continuing with client logout:', apiError);
      // 即使伺服器登出失敗，也繼續執行客戶端登出
    }

    // 2. 清理本地資料和 token
    clearUserData();
    clearAuthTokens();

    // 3. 執行 LIFF 登出（這會清除 LIFF 的 access token）
    liff.logout();

    // 4. 短暫延遲後重新載入頁面，確保登出完成
    setTimeout(() => {
      window.location.href = '/';
    }, 100);

  } catch (error) {
    console.error('Logout failed:', error);
    // 即使出錯也嘗試重新載入頁面
    window.location.reload();
  }
};

// 關閉 LIFF 視窗
export const closeLiff = (): void => {
  liff.closeWindow();
};

// 獲取 LINE 用戶的 ID Token
export const getIdToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  if (!liff.isLoggedIn()) {
    return null;
  }
  return liff.getIDToken() || null;
};

// 獲取 LINE 用戶的 Access Token
export const getAccessToken = (): string | null => {
  if (typeof window === 'undefined') return null;

  // 開發模式
  if (isDevMode()) {
    return liffDevMock.getAccessToken();
  }

  if (!liff.isLoggedIn()) {
    return null;
  }
  return liff.getAccessToken() || null;
};

// 打開外部連結
export const openExternalLink = (url: string): void => {
  liff.openWindow({
    url,
    external: true,
  });
};

// 分享訊息
export const shareMessage = async (messages: any[]): Promise<void> => {
  if (!liff.isInClient()) {
    console.error('Share message is only available in LINE app');
    return;
  }

  try {
    await liff.shareTargetPicker(messages);
  } catch (error) {
    console.error('Failed to share message', error);
    throw error;
  }
};

// 發送訊息到 LINE 聊天室
export const sendMessages = async (messages: any[]): Promise<void> => {
  if (typeof window === 'undefined') {
    console.error('sendMessages is only available in browser environment');
    return;
  }

  // 開發模式
  if (isDevMode()) {
    console.log('Development mode: Simulating sendMessages', messages);
    return;
  }

  if (!liff.isInClient()) {
    console.error('sendMessages is only available in LINE app');
    return;
  }

  try {
    await liff.sendMessages(messages);
    console.log('Messages sent successfully');
  } catch (error) {
    console.error('Failed to send messages', error);
    throw error;
  }
};

export default {
  initializeLiff,
  isInLiffBrowser,
  isLoggedIn,
  getUserProfile,
  login,
  loginWithBackend,
  ensureBackendLogin,
  logout,
  logoutWithCleanup,
  clearUserData,
  clearAuthTokens,
  closeLiff,
  getIdToken,
  getAccessToken,
  openExternalLink,
  shareMessage,
  sendMessages,
};





