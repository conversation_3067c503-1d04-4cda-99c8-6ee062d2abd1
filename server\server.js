const app = require('./app');
const db = require('./models');
const { initializeDefaultSettings } = require('./controllers/settings.controller');
const http = require('http');
const SocketHandler = require('./socket/socketHandler');

// 設置端口
const PORT = process.env.PORT || 5000;

// 創建 HTTP 服務器
const server = http.createServer(app);

// 初始化 Socket.io
const socketHandler = new SocketHandler(server);

// 將 socketHandler 添加到 app 中，供其他模組使用
app.set('socketHandler', socketHandler);

// 打印環境變量（不包含密碼）
console.log('環境變量:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('NODE_ENV:', process.env.NODE_ENV);

// 測試資料庫連接
console.log('嘗試連接資料庫...');
db.sequelize.authenticate()
  .then(async () => {
    console.log('資料庫連接成功');

    // 初始化預設設定
    await initializeDefaultSettings();

    // 啟動伺服器
    server.listen(PORT, () => {
      console.log(`伺服器運行在端口 ${PORT}`);
      console.log(`WebSocket 服務已啟動`);
    });
  })
  .catch(err => {
    console.error('資料庫連接失敗:', err);
  });

// 添加示例數據
async function seedDatabase() {
  try {
    // 添加示例產品
    const products = [
      { name: '珍珠奶茶', category: '奶茶', price: 60, description: '經典珍珠奶茶，香濃奶茶搭配QQ珍珠', image: 'pearl-milk-tea.jpg' },
      { name: '四季春茶', category: '茶類', price: 40, description: '清香四季春茶，回甘無比', image: 'four-seasons-tea.jpg' },
      { name: '芒果冰沙', category: '果汁', price: 70, description: '新鮮芒果製成，酸甜可口', image: 'mango-smoothie.jpg' },
      { name: '美式咖啡', category: '咖啡', price: 50, description: '濃郁的美式咖啡，提神醒腦', image: 'americano.jpg' },
      { name: '布丁奶茶', category: '特調', price: 65, description: '奶茶中加入滑嫩布丁，口感豐富', image: 'pudding-milk-tea.jpg' }
    ];

    for (const product of products) {
      const createdProduct = await db.products.create(product);

      // 添加產品選項
      if (product.category === '茶類' || product.category === '奶茶' || product.category === '果汁') {
        // 糖度選項
        const sugarOptions = ['無糖', '微糖', '半糖', '少糖', '全糖'];
        for (const sugar of sugarOptions) {
          await db.productOptions.create({
            productId: createdProduct.id,
            optionType: 'sugar',
            optionName: sugar,
            additionalPrice: 0
          });
        }

        // 冰塊選項
        const iceOptions = ['去冰', '微冰', '少冰', '正常冰'];
        for (const ice of iceOptions) {
          await db.productOptions.create({
            productId: createdProduct.id,
            optionType: 'ice',
            optionName: ice,
            additionalPrice: 0
          });
        }
      }

      // 配料選項
      if (product.category === '奶茶' || product.category === '特調') {
        const toppingOptions = [
          { name: '珍珠', price: 10 },
          { name: '椰果', price: 10 },
          { name: '布丁', price: 15 },
          { name: '芋圓', price: 15 },
          { name: '仙草', price: 10 }
        ];

        for (const topping of toppingOptions) {
          await db.productOptions.create({
            productId: createdProduct.id,
            optionType: 'topping',
            optionName: topping.name,
            additionalPrice: topping.price
          });
        }
      }
    }

    console.log(`已添加 ${products.length} 個產品和相關選項`);
  } catch (error) {
    console.error('添加示例數據失敗:', error);
    throw error;
  }
}
