const db = require('../models');
const SystemSetting = db.systemSettings;

// 獲取所有系統設定
exports.getAllSettings = async (req, res) => {
  try {
    const settings = await SystemSetting.findAll();
    
    // 轉換為鍵值對格式
    const settingsMap = {};
    settings.forEach(setting => {
      let value = setting.value;
      
      // 根據類型轉換值
      switch (setting.type) {
        case 'number':
          value = parseFloat(value);
          break;
        case 'boolean':
          value = value === 'true';
          break;
        case 'json':
          try {
            value = JSON.parse(value);
          } catch (e) {
            value = setting.value;
          }
          break;
        default:
          value = setting.value;
      }
      
      settingsMap[setting.key] = value;
    });
    
    res.status(200).json(settingsMap);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '獲取系統設定失敗' });
  }
};

// 獲取單個設定
exports.getSetting = async (req, res) => {
  try {
    const { key } = req.params;
    const setting = await SystemSetting.findOne({ where: { key } });
    
    if (!setting) {
      return res.status(404).json({ message: '設定不存在' });
    }
    
    let value = setting.value;
    
    // 根據類型轉換值
    switch (setting.type) {
      case 'number':
        value = parseFloat(value);
        break;
      case 'boolean':
        value = value === 'true';
        break;
      case 'json':
        try {
          value = JSON.parse(value);
        } catch (e) {
          value = setting.value;
        }
        break;
      default:
        value = setting.value;
    }
    
    res.status(200).json({ key: setting.key, value, description: setting.description });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '獲取設定失敗' });
  }
};

// 更新設定
exports.updateSetting = async (req, res) => {
  try {
    const { key } = req.params;
    const { value, description } = req.body;
    
    const setting = await SystemSetting.findOne({ where: { key } });
    
    if (!setting) {
      return res.status(404).json({ message: '設定不存在' });
    }
    
    // 轉換值為字符串存儲
    let stringValue = value;
    if (typeof value === 'object') {
      stringValue = JSON.stringify(value);
    } else {
      stringValue = String(value);
    }
    
    await setting.update({ 
      value: stringValue,
      description: description || setting.description
    });
    
    res.status(200).json({ message: '設定更新成功', setting });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '更新設定失敗' });
  }
};

// 創建設定
exports.createSetting = async (req, res) => {
  try {
    const { key, value, description, type = 'string' } = req.body;
    
    // 檢查設定是否已存在
    const existingSetting = await SystemSetting.findOne({ where: { key } });
    if (existingSetting) {
      return res.status(400).json({ message: '設定已存在' });
    }
    
    // 轉換值為字符串存儲
    let stringValue = value;
    if (typeof value === 'object') {
      stringValue = JSON.stringify(value);
    } else {
      stringValue = String(value);
    }
    
    const setting = await SystemSetting.create({
      key,
      value: stringValue,
      description,
      type
    });
    
    res.status(201).json({ message: '設定創建成功', setting });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: '創建設定失敗' });
  }
};

// 初始化預設設定
exports.initializeDefaultSettings = async () => {
  try {
    const defaultSettings = [
      {
        key: 'order_wait_timeout',
        value: '60',
        description: '訂單等待接單超時時間（秒）',
        type: 'number'
      },
      {
        key: 'enable_order_notifications',
        value: 'true',
        description: '是否啟用訂單即時通知',
        type: 'boolean'
      },
      {
        key: 'max_upload_file_size',
        value: '5',
        description: '最大上傳文件大小（MB）',
        type: 'number'
      }
    ];
    
    for (const defaultSetting of defaultSettings) {
      const existing = await SystemSetting.findOne({ where: { key: defaultSetting.key } });
      if (!existing) {
        await SystemSetting.create(defaultSetting);
        console.log(`✓ 創建預設設定: ${defaultSetting.key}`);
      }
    }
  } catch (error) {
    console.error('初始化預設設定失敗:', error);
  }
};
