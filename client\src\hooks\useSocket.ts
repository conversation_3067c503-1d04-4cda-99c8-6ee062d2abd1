'use client';

import { useEffect, useState, useCallback } from 'react';
import socketManager from '@/utils/socket';
import { Socket } from 'socket.io-client';

interface OrderEvent {
  orderId: number;
  orderNumber: string;
  message: string;
  status?: string;
}

interface UseSocketReturn {
  socket: Socket | null;
  isConnected: boolean;
  connect: () => Promise<void>;
  disconnect: () => void;
  onOrderAccepted: (callback: (data: OrderEvent) => void) => void;
  onOrderTimeout: (callback: (data: OrderEvent) => void) => void;
  onOrderStatusUpdated: (callback: (data: OrderEvent) => void) => void;
  removeAllListeners: () => void;
}

export const useSocket = (): UseSocketReturn => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  const connect = useCallback(async () => {
    try {
      const socketInstance = await socketManager.connect();
      setSocket(socketInstance);
      setIsConnected(true);
    } catch (error) {
      console.error('Socket 連接失敗:', error);
      setIsConnected(false);
    }
  }, []);

  const disconnect = useCallback(() => {
    socketManager.disconnect();
    setSocket(null);
    setIsConnected(false);
  }, []);

  const onOrderAccepted = useCallback((callback: (data: OrderEvent) => void) => {
    socketManager.onOrderAccepted(callback);
  }, []);

  const onOrderTimeout = useCallback((callback: (data: OrderEvent) => void) => {
    socketManager.onOrderTimeout(callback);
  }, []);

  const onOrderStatusUpdated = useCallback((callback: (data: OrderEvent) => void) => {
    socketManager.onOrderStatusUpdated(callback);
  }, []);

  const removeAllListeners = useCallback(() => {
    socketManager.offOrderAccepted();
    socketManager.offOrderTimeout();
    socketManager.offOrderStatusUpdated();
  }, []);

  useEffect(() => {
    // 檢查連接狀態
    const checkConnection = () => {
      const isSocketConnected = socketManager.isSocketConnected();
      setIsConnected(isSocketConnected);
      setSocket(socketManager.getSocket());
    };

    // 定期檢查連接狀態
    const interval = setInterval(checkConnection, 1000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  return {
    socket,
    isConnected,
    connect,
    disconnect,
    onOrderAccepted,
    onOrderTimeout,
    onOrderStatusUpdated,
    removeAllListeners
  };
};
