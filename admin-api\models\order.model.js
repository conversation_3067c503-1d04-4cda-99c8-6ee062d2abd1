module.exports = (sequelize, Sequelize) => {
  const Order = sequelize.define('order', {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    orderNumber: {
      type: Sequelize.STRING,
      unique: true,
      allowNull: false,
      comment: '訂單編號，格式：ORD-YYYYMMDD-XXX'
    },
    userId: {
      type: Sequelize.INTEGER,
      allowNull: false
    },
    totalPrice: {
      type: Sequelize.FLOAT,
      allowNull: false
    },
    status: {
      type: Sequelize.ENUM('pending', 'processing', 'completed', 'cancelled'),
      defaultValue: 'pending',
      comment: '訂單狀態：pending(待處理), processing(製作中), completed(已完成), cancelled(已取消)'
    },
    orderType: {
      type: Sequelize.ENUM('immediate', 'scheduled'),
      defaultValue: 'immediate',
      comment: '訂單類型：immediate(立即訂購), scheduled(預約訂購)'
    },
    scheduledTime: {
      type: Sequelize.DATE,
      comment: '預約時間'
    },
    note: {
      type: Sequelize.TEXT,
      comment: '訂單備註'
    }
  }, {
    timestamps: true
  });

  return Order;
};
