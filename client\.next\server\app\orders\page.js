(()=>{var e={};e.id=778,e.ids=[778],e.modules={440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},769:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(687);s(3210);var a=s(6189),l=s(584),i=s(7979),n=s(3166),c=s(3613),d=s(8869),o=s(5814),x=s.n(o);function m({children:e,requireAuth:t=!0,requireVerification:s=!0}){(0,a.useRouter)();let{isInitialized:o,isLoggedIn:m}=(0,l.x)(),{isFullyVerified:u,loading:p}=(0,i.h)();return!o||p?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center gradient-bg",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 gradient-orange rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,r.jsx)(n.A,{className:"w-8 h-8 text-white"})}),(0,r.jsx)("p",{className:"text-lg font-medium text-gray-700",children:"載入中..."}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"正在檢查會員狀態"})]})}):t&&!m?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center gradient-bg",children:(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl shadow-warm p-8 max-w-md mx-4 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(c.A,{className:"w-8 h-8 text-red-500"})}),(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"需要登入"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"請先登入以使用此功能"}),(0,r.jsx)(x(),{href:"/",className:"w-full py-3 px-6 gradient-green text-white font-semibold rounded-2xl btn-hover shadow-warm inline-block",children:"返回首頁"})]})}):s&&m&&!u?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center gradient-bg",children:(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-3xl shadow-warm p-8 max-w-md mx-4 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(d.A,{className:"w-8 h-8 text-blue-500"})}),(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"需要會員驗證"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"請先完成會員註冊和手機驗證以使用此功能"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(x(),{href:"/register",className:"w-full py-3 px-6 bg-blue-500 text-white font-semibold rounded-2xl btn-hover shadow-warm inline-block",children:"立即註冊"}),(0,r.jsx)(x(),{href:"/",className:"w-full py-3 px-6 bg-gray-200 text-gray-700 font-semibold rounded-2xl btn-hover inline-block",children:"返回首頁"})]})]})}):(0,r.jsx)(r.Fragment,{children:e})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1057:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},2412:e=>{"use strict";e.exports=require("assert")},2595:(e,t,s)=>{Promise.resolve().then(s.bind(s,4346))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3515:(e,t,s)=>{"use strict";s.d(t,{_:()=>a});var r=s(3210);let a=()=>{let[e,t]=(0,r.useState)([]),[s,a]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{t(JSON.parse(e))}catch(e){console.error("Failed to load cart from localStorage:",e)}},[]),(0,r.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(e))},[e]);let l=e=>{t(t=>t.filter(t=>t.id!==e))},i=()=>e.reduce((e,t)=>e+t.totalPrice*t.quantity,0),n=0===e.length;return{cartItems:e,isLoading:s,addToCart:(e,s,r)=>{let a=r||e.price,l={id:Date.now(),productId:e.id,name:e.name,basePrice:e.price,totalPrice:a,quantity:1,image:e.image,customizations:s};t(t=>{let r=t.findIndex(t=>t.productId===e.id&&JSON.stringify(t.customizations)===JSON.stringify(s));if(!(r>-1))return[...t,l];{let e=[...t];return e[r].quantity+=1,e}})},updateQuantity:(e,s)=>{if(s<=0)return void l(e);t(t=>t.map(t=>t.id===e?{...t,quantity:s}:t))},removeFromCart:l,clearCart:()=>{t([])},getTotalPrice:i,getTotalQuantity:()=>e.reduce((e,t)=>e+t.quantity,0),isEmpty:n,formatOrderData:(t,s,r)=>({items:e.map(e=>({productId:e.productId,quantity:e.quantity,price:e.totalPrice,customizations:e.customizations||{}})),totalPrice:i(),orderType:t,scheduledTime:s,note:r})}}},3613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3649:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4346:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\0_project\\\\augment-projects\\\\cocodrink-line\\\\client\\\\src\\\\app\\\\orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\orders\\page.tsx","default")},4735:e=>{"use strict";e.exports=require("events")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5694:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=s(5239),a=s(8088),l=s(8170),i=s.n(l),n=s(893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d={children:["",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4346)),"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\orders\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\0_project\\augment-projects\\cocodrink-line\\client\\src\\app\\orders\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/orders/page",pathname:"/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7679:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(687),a=s(769),l=s(584),i=s(5814),n=s.n(i),c=s(2688);let d=(0,c.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var o=s(3166),x=s(5336);let m=(0,c.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var u=s(1057),p=s(8559);let h=(0,c.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var g=s(3649),b=s(3210),f=s(7327),j=s(7796),y=s(3515),N=s(1860);function v({isOpen:e,onClose:t,result:s}){if(!e||!s)return null;let a=s.unavailableItems.length>0||s.unavailableToppings.length>0;return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-warm max-w-md w-full max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"sticky top-0 bg-white rounded-t-3xl border-b border-gray-100 p-6 flex items-center justify-between",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"再點一次結果"}),(0,r.jsx)("button",{onClick:t,className:"p-2 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors",children:(0,r.jsx)(N.A,{className:"w-5 h-5"})})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-800",children:"成功加入購物車"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["已成功重新加入 ",s.successCount," 項商品"]})]})]}),a&&(0,r.jsxs)("div",{className:"bg-orange-50 border border-orange-200 rounded-xl p-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,r.jsx)(g.A,{className:"w-5 h-5 text-orange-500"}),(0,r.jsx)("h4",{className:"font-semibold text-orange-800",children:"注意事項"})]}),s.unavailableItems.length>0&&(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-orange-800 mb-2",children:"以下商品已下架或不可用："}),(0,r.jsx)("ul",{className:"text-sm text-orange-700 space-y-1",children:s.unavailableItems.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-1.5 h-1.5 bg-orange-500 rounded-full"}),e]},t))})]}),s.unavailableToppings.length>0&&(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-orange-800 mb-2",children:"以下配料已不可用："}),(0,r.jsx)("ul",{className:"text-sm text-orange-700 space-y-1",children:s.unavailableToppings.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-1.5 h-1.5 bg-orange-500 rounded-full"}),e]},t))})]}),(0,r.jsx)("p",{className:"text-sm text-orange-700 font-medium",children:"價格已依據最新價格重新計算"})]}),(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-xl p-4 mb-6",children:(0,r.jsx)("p",{className:"text-sm text-blue-700",children:"\uD83D\uDCA1 商品已加入購物車，您可以前往購物車查看或繼續購物"})}),(0,r.jsx)("button",{onClick:t,className:"w-full py-3 px-6 gradient-green text-white rounded-2xl font-semibold btn-hover shadow-warm",children:"我知道了"})]})]})})}function w(){let{userProfile:e}=(0,l.x)(),{addToCart:t}=(0,y._)(),[s,i]=(0,b.useState)("all"),[c,N]=(0,b.useState)([]),[w,k]=(0,b.useState)(!0),[A,q]=(0,b.useState)(null),[z,C]=(0,b.useState)(!1),[P,S]=(0,b.useState)(null),[_,D]=(0,b.useState)(null),[T,M]=(0,b.useState)(null),[I,E]=(0,b.useState)(!1),[F,O]=(0,b.useState)(null),[$,G]=(0,b.useState)(""),J=[{id:"all",name:"全部",color:"gray"},{id:"pending",name:"處理中",color:"yellow"},{id:"processing",name:"製作中",color:"blue"},{id:"completed",name:"已完成",color:"green"},{id:"cancelled",name:"已取消",color:"red"}],L=async()=>{try{k(!0);let e=await f.A.user.getOrders();N(e)}catch(e){console.error("Failed to fetch orders:",e),D("無法載入訂單列表")}finally{k(!1)}},R=e=>{O(e),E(!0),G("")},V=async()=>{if(F){M(F.id);try{"pending"===F.status?(await f.A.orders.cancel(F.id),alert("訂單已成功取消")):"processing"===F.status&&(await f.A.orders.requestCancel(F.id,$),alert("取消請求已發送，等待管理員確認")),await L()}catch(e){console.error("Cancel order failed:",e),alert(e.response?.data?.message||"取消訂單失敗，請稍後再試")}finally{M(null),E(!1),O(null),G("")}}},Q=async e=>{q(e.id);try{let s=await f.A.products.getAll(),r=[],a=[],l=0;for(let i of e.items){let e=s.find(e=>e.id===i.productId);if(!e){r.push(i.product?.name||"未知商品");continue}if(!e.isAvailable){r.push(e.name);continue}let n=e.options?.filter(e=>"topping"===e.optionType)||[],c=i.customizations?.toppings||[],d=c.filter(e=>n.some(t=>t.optionName===e)),o=c.filter(e=>!n.some(t=>t.optionName===e));a.push(...o);let x=e.price;d.forEach(e=>{let t=n.find(t=>t.optionName===e);t&&(x+=t.additionalPrice)});let m={sugar:i.customizations?.sugar||"正常糖",ice:i.customizations?.ice||"正常冰",toppings:d};for(let s=0;s<i.quantity;s++)t(e,m,x);l++}S({successCount:l,unavailableItems:r,unavailableToppings:a}),C(!0)}catch(e){console.error("重新訂購失敗:",e),alert("重新訂購失敗，請稍後再試")}finally{q(null)}},W=e=>"pending"===e||"processing"===e,H=e=>{switch(e){case"pending":return(0,r.jsx)(d,{className:"w-5 h-5 text-yellow-500"});case"processing":return(0,r.jsx)(o.A,{className:"w-5 h-5 text-blue-500"});case"completed":return(0,r.jsx)(x.A,{className:"w-5 h-5 text-green-500"});case"cancelled":return(0,r.jsx)(m,{className:"w-5 h-5 text-red-500"});default:return(0,r.jsx)(d,{className:"w-5 h-5 text-gray-500"})}},K=e=>{let t=J.find(t=>t.id===e);return t?t.name:e},X=e=>{let t=J.find(t=>t.id===e);return t?t.color:"gray"},Z="all"===s?c:c.filter(e=>e.status===s),B=e=>{let t=new Date(e);return{date:t.toLocaleDateString("zh-TW"),time:t.toLocaleTimeString("zh-TW",{hour:"2-digit",minute:"2-digit"})}};return w?(0,r.jsx)(a.A,{requireAuth:!0,requireVerification:!0,children:(0,r.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"載入訂單中..."})]})})}):_?(0,r.jsx)(a.A,{requireAuth:!0,requireVerification:!0,children:(0,r.jsx)("div",{className:"min-h-screen gradient-bg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(u.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-red-500 mb-4",children:_}),(0,r.jsx)("button",{onClick:L,className:"px-6 py-3 bg-purple-500 text-white rounded-full hover:bg-purple-600 transition-colors",children:"重新載入"})]})})}):(0,r.jsx)(a.A,{requireAuth:!0,requireVerification:!0,children:(0,r.jsxs)("div",{className:"min-h-screen gradient-bg",children:[(0,r.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-soft sticky top-0 z-50",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(n(),{href:"/",className:"p-2 bg-purple-100 text-purple-600 rounded-full hover:bg-purple-200 transition-colors",children:(0,r.jsx)(p.A,{className:"w-5 h-5"})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"w-6 h-6 text-purple-500"}),(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"我的訂單"})]})]}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:e?.displayName})]})}),(0,r.jsxs)("main",{className:"container mx-auto px-4 py-6",children:[(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:"訂單記錄"}),(0,r.jsx)("p",{className:"text-gray-600",children:"查看您的訂單狀態和歷史記錄"})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("div",{className:"flex gap-2 overflow-x-auto pb-2",children:J.map(e=>(0,r.jsx)("button",{onClick:()=>i(e.id),className:`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${s===e.id?"bg-purple-500 text-white":"bg-white text-gray-600 hover:bg-purple-100"}`,children:e.name},e.id))})}),(0,r.jsx)("div",{className:"space-y-4",children:Z.map(e=>{let{date:t,time:s}=B(e.createdAt);return(0,r.jsxs)("div",{className:"bg-white/90 backdrop-blur-sm rounded-2xl shadow-warm p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-bold text-gray-800",children:["訂單 #",e.id]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[t," ",s]}),"scheduled"===e.orderType&&e.scheduledTime&&(0,r.jsxs)("p",{className:"text-sm text-orange-600",children:["預約時間: ",B(e.scheduledTime).date," ",B(e.scheduledTime).time]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[H(e.status),(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${"yellow"===X(e.status)?"bg-yellow-100 text-yellow-800":"blue"===X(e.status)?"bg-blue-100 text-blue-800":"green"===X(e.status)?"bg-green-100 text-green-800":"red"===X(e.status)?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:K(e.status)})]})]}),(0,r.jsx)("div",{className:"space-y-3 mb-4",children:e.items&&e.items.map((e,t)=>(0,r.jsx)(j.A,{item:e,canEdit:!1},t))}),e.note&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-gray-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"備註:"})," ",e.note]})}),(0,r.jsxs)("div",{className:"border-t pt-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,r.jsx)("span",{className:"font-bold text-gray-800",children:"總計"}),(0,r.jsxs)("span",{className:"font-bold text-purple-500 text-lg",children:["NT$ ",e.totalPrice]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("button",{onClick:()=>Q(e),disabled:A===e.id,className:"w-full py-2 px-4 bg-green-100 text-green-600 rounded-lg hover:bg-green-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:A===e.id?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin"}),"處理中..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h,{className:"w-4 h-4"}),"再點一次"]})}),W(e.status)&&(0,r.jsx)("button",{onClick:()=>R(e),disabled:T===e.id,className:"w-full py-2 px-4 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2",children:T===e.id?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin"}),"處理中..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m,{className:"w-4 h-4"}),"pending"===e.status?"取消訂單":"申請取消"]})})]})]})]},e.id)})}),0===Z.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(u.A,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"暫無訂單記錄"}),(0,r.jsx)(n(),{href:"/products",className:"inline-block mt-4 px-6 py-3 bg-purple-500 text-white rounded-full hover:bg-purple-600 transition-colors",children:"開始點餐"})]})]}),I&&F&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-warm max-w-md w-full p-6",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[(0,r.jsx)(g.A,{className:"w-16 h-16 text-orange-500 mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-800 mb-2",children:"pending"===F.status?"確認取消訂單":"申請取消訂單"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["訂單 #",F.id]})]}),"pending"===F.status?(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("p",{className:"text-gray-700 text-center",children:"此訂單目前處於處理中狀態，可以直接取消。"})}):(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("p",{className:"text-gray-700 mb-3",children:"此訂單正在製作中，需要管理員確認才能取消。請說明取消原因："}),(0,r.jsx)("textarea",{value:$,onChange:e=>G(e.target.value),placeholder:"請輸入取消原因...",className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500",rows:3,required:!0})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:()=>{E(!1),O(null),G("")},className:"flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors",children:"取消"}),(0,r.jsx)("button",{onClick:V,disabled:"processing"===F.status&&!$.trim(),className:"flex-1 py-3 px-4 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"pending"===F.status?"確認取消":"發送申請"})]})]})}),(0,r.jsx)(v,{isOpen:z,onClose:()=>{C(!1),S(null)},result:P})]})})}},7796:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(687);function a({item:e,onEditItem:t,canEdit:s=!1}){return(0,r.jsx)("div",{className:"border-l-4 border-purple-200 pl-4 py-3 bg-gray-50 rounded-r-lg",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[s&&t?(0,r.jsxs)("button",{onClick:()=>t(e),className:"font-medium text-purple-600 hover:text-purple-800 hover:underline transition-colors",children:[e.product?.name||"商品"," \xd7 ",e.quantity]}):(0,r.jsxs)("span",{className:"font-medium text-gray-800",children:[e.product?.name||"商品"," \xd7 ",e.quantity]}),s&&(0,r.jsx)("span",{className:"text-xs text-gray-400",children:"(點擊修改)"})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["原價: NT$ ",e.product?.price||0]}),(()=>{if(!e.customizations)return null;let t=[];return e.customizations.sugar&&t.push((0,r.jsxs)("span",{className:"text-xs text-orange-600",children:["\uD83C\uDF6F ",e.customizations.sugar]},"sugar")),e.customizations.ice&&t.push((0,r.jsxs)("span",{className:"text-xs text-blue-600",children:["\uD83E\uDDCA ",e.customizations.ice]},"ice")),t.length>0?(0,r.jsx)("div",{className:"flex items-center gap-3 mt-1",children:t}):null})(),(()=>{if(!e.customizations?.toppings||0===e.customizations.toppings.length)return null;let t=e=>({珍珠:10,椰果:10,鮮奶油:15,巧克力片:10,額外奶泡:10,布丁:12,仙草:8})[e]||10;return(0,r.jsx)("div",{className:"mt-2 space-y-1",children:e.customizations.toppings.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,r.jsxs)("span",{className:"text-green-600 flex items-center gap-1",children:[(0,r.jsx)("span",{children:"\uD83E\uDD64"}),(0,r.jsx)("span",{children:e})]}),(0,r.jsxs)("span",{className:"text-orange-500 font-medium",children:["+NT$ ",t(e)]})]},s))})})()]}),(0,r.jsxs)("div",{className:"text-right ml-4",children:[(0,r.jsxs)("div",{className:"font-semibold text-gray-800",children:["NT$ ",e.price*e.quantity]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["單價: NT$ ",e.price]})]})]})})}},7910:e=>{"use strict";e.exports=require("stream")},8251:(e,t,s)=>{Promise.resolve().then(s.bind(s,7679))},8354:e=>{"use strict";e.exports=require("util")},8559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,41,248,947],()=>s(5694));module.exports=r})();